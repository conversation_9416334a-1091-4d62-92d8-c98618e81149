<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supply extends Model
{
    protected $fillable = [
        'po_received_id',
        'user_id',
        'supply_type',
        'supplied_amount',
        'notes',
    ];

    protected $casts = [
        'supplied_amount' => 'decimal:2',
    ];

    public function poReceived(): BelongsTo
    {
        return $this->belongsTo(PoReceived::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }
}
