<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Delivery Challan</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .document-title {
            font-size: 18px;
            font-weight: bold;
            color: #666;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .total-section {
            text-align: right;
            margin-top: 20px;
        }
        .total-amount {
            font-size: 16px;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        .signature-box {
            text-align: center;
            width: 200px;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">Supply Management System</div>
        <div class="document-title">DELIVERY CHALLAN</div>
    </div>

    <div class="info-section">
        <div class="info-row">
            <span class="info-label">DC Number:</span>
            <span>DC-<?php echo e($supply->id); ?>-<?php echo e(date('Y')); ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Date:</span>
            <span><?php echo e($supply->created_at->format('d/m/Y')); ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">PO Number:</span>
            <span><?php echo e($supply->poReceived->po_number); ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Institution:</span>
            <span><?php echo e($supply->poReceived->institution_name); ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Address:</span>
            <span><?php echo e($supply->poReceived->address); ?></span>
        </div>
        <div class="info-row">
            <span class="info-label">Supply Type:</span>
            <span><?php echo e(ucfirst($supply->supply_type)); ?></span>
        </div>
    </div>

    <table class="table">
        <thead>
            <tr>
                <th>S.No</th>
                <th>Product Name</th>
                <th>Batch No</th>
                <th>Mfg Date</th>
                <th>Expiry Date</th>
                <th>Quantity</th>
                <th>Price (PKR)</th>
                <th>Total (PKR)</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $supply->poReceived->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($index + 1); ?></td>
                <td><?php echo e($item->product_name); ?></td>
                <td><?php echo e($item->batch_no); ?></td>
                <td><?php echo e($item->mfg_date->format('d/m/Y')); ?></td>
                <td><?php echo e($item->expiry_date->format('d/m/Y')); ?></td>
                <td><?php echo e($item->quantity); ?></td>
                <td><?php echo e(number_format($item->price, 2)); ?></td>
                <td><?php echo e(number_format($item->total, 2)); ?></td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    <div class="total-section">
        <div class="total-amount">
            Supplied Amount: PKR <?php echo e(number_format($supply->supplied_amount, 2)); ?>

        </div>
    </div>

    <?php if($supply->notes): ?>
    <div class="info-section">
        <div class="info-row">
            <span class="info-label">Notes:</span>
            <span><?php echo e($supply->notes); ?></span>
        </div>
    </div>
    <?php endif; ?>

    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-line">Supplier Signature</div>
        </div>
        <div class="signature-box">
            <div class="signature-line">Receiver Signature</div>
        </div>
    </div>

    <div class="footer">
        <p><strong>Generated by:</strong> <?php echo e($supply->user->name); ?></p>
        <p><strong>Generated on:</strong> <?php echo e(now()->format('d/m/Y H:i:s')); ?></p>
    </div>
</body>
</html>
<?php /**PATH F:\xampp\htdocs\supply_system\resources\views/documents/dc.blade.php ENDPATH**/ ?>