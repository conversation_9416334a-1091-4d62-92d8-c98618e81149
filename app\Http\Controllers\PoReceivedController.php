<?php

namespace App\Http\Controllers;

use App\Models\PoReceived;
use App\Models\PoItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class PoReceivedController extends Controller
{
    public function index()
    {
        $poReceived = PoReceived::with(['user', 'items'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('PoReceived/Index', [
            'poReceived' => $poReceived
        ]);
    }

    public function create()
    {
        return Inertia::render('PoReceived/Create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'po_number' => 'required|string|unique:po_received',
            'po_date' => 'required|date',
            'po_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'institution_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'required|string',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.batch_no' => 'required|string|max:255',
            'items.*.mfg_date' => 'required|date',
            'items.*.expiry_date' => 'required|date|after:mfg_date',
            'items.*.total' => 'required|numeric|min:0',
        ]);

        $poImagePath = null;
        if ($request->hasFile('po_image')) {
            $poImagePath = $request->file('po_image')->store('po_images', 'public');
        }

        $grandTotal = collect($request->items)->sum('total');

        $poReceived = PoReceived::create([
            'user_id' => auth()->id(),
            'po_number' => $request->po_number,
            'po_date' => $request->po_date,
            'po_image' => $poImagePath,
            'institution_name' => $request->institution_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'grand_total' => $grandTotal,
            'currency' => 'PKR',
            'status' => 'pending',
        ]);

        foreach ($request->items as $item) {
            PoItem::create([
                'po_received_id' => $poReceived->id,
                'product_name' => $item['product_name'],
                'price' => $item['price'],
                'quantity' => $item['quantity'],
                'batch_no' => $item['batch_no'],
                'mfg_date' => $item['mfg_date'],
                'expiry_date' => $item['expiry_date'],
                'total' => $item['total'],
            ]);
        }

        return redirect()->route('po-received.index')->with('success', 'PO Received created successfully.');
    }

    public function show(PoReceived $poReceived)
    {
        $poReceived->load(['user', 'items', 'supplies.user']);

        return Inertia::render('PoReceived/Show', [
            'poReceived' => $poReceived
        ]);
    }

    public function edit(PoReceived $poReceived)
    {
        $poReceived->load('items');

        return Inertia::render('PoReceived/Edit', [
            'poReceived' => $poReceived
        ]);
    }

    public function update(Request $request, PoReceived $poReceived)
    {
        $request->validate([
            'po_number' => 'required|string|unique:po_received,po_number,' . $poReceived->id,
            'po_date' => 'required|date',
            'po_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'institution_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'required|string',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.batch_no' => 'required|string|max:255',
            'items.*.mfg_date' => 'required|date',
            'items.*.expiry_date' => 'required|date|after:mfg_date',
            'items.*.total' => 'required|numeric|min:0',
        ]);

        $poImagePath = $poReceived->po_image;
        if ($request->hasFile('po_image')) {
            if ($poImagePath) {
                Storage::disk('public')->delete($poImagePath);
            }
            $poImagePath = $request->file('po_image')->store('po_images', 'public');
        }

        $grandTotal = collect($request->items)->sum('total');

        $poReceived->update([
            'po_number' => $request->po_number,
            'po_date' => $request->po_date,
            'po_image' => $poImagePath,
            'institution_name' => $request->institution_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'grand_total' => $grandTotal,
        ]);

        // Delete existing items and create new ones
        $poReceived->items()->delete();

        foreach ($request->items as $item) {
            PoItem::create([
                'po_received_id' => $poReceived->id,
                'product_name' => $item['product_name'],
                'price' => $item['price'],
                'quantity' => $item['quantity'],
                'batch_no' => $item['batch_no'],
                'mfg_date' => $item['mfg_date'],
                'expiry_date' => $item['expiry_date'],
                'total' => $item['total'],
            ]);
        }

        return redirect()->route('po-received.index')->with('success', 'PO Received updated successfully.');
    }

    public function destroy(PoReceived $poReceived)
    {
        if ($poReceived->po_image) {
            Storage::disk('public')->delete($poReceived->po_image);
        }

        $poReceived->delete();

        return redirect()->route('po-received.index')->with('success', 'PO Received deleted successfully.');
    }
}
