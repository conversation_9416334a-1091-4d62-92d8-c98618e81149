<?php

namespace App\Http\Controllers;

use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class DocumentController extends Controller
{
    public function index()
    {
        $documents = Document::with(['supply.poReceived', 'supply.user'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Documents/Index', [
            'documents' => $documents
        ]);
    }

    public function show(Document $document)
    {
        $document->load(['supply.poReceived', 'supply.user']);

        return Inertia::render('Documents/Show', [
            'document' => $document
        ]);
    }

    public function download(Document $document)
    {
        if (!Storage::disk('public')->exists($document->document_path)) {
            abort(404, 'Document not found');
        }

        return Storage::disk('public')->download($document->document_path);
    }

    public function uploadImage(Request $request, Document $document)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Delete old image if exists
        if ($document->uploaded_image_path) {
            Storage::disk('public')->delete($document->uploaded_image_path);
        }

        $imagePath = $request->file('image')->store('document_images', 'public');

        $document->update([
            'uploaded_image_path' => $imagePath
        ]);

        return redirect()->back()->with('success', 'Image uploaded successfully.');
    }
}
