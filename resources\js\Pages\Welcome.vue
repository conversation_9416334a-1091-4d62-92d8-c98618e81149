&lt;script setup lang="ts">
import { Head, Link } from '@inertiajs/vue3';
import AppLayout from '@/layouts/app/AppSidebarLayout.vue';

const breadcrumbs = [
    {
        title: 'Welcome',
        href: '/',
    },
];
&lt;/script>

&lt;template>
    &lt;Head title="Welcome" />

    &lt;AppLayout :breadcrumbs="breadcrumbs">
        &lt;div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            &lt;div>
                &lt;h1 class="text-3xl font-bold">Welcome to Supply System&lt;/h1>
                &lt;p class="text-muted-foreground">Manage your supplies, purchase orders, and payments efficiently&lt;/p>
            &lt;/div>

            &lt;div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                &lt;Link 
                    v-for="(item, index) in [
                        { title: 'Purchase Orders', description: 'Manage and track purchase orders', href: '/po-received' },
                        { title: 'Supplies', description: 'Track supplies and inventory', href: '/supply' },
                        { title: 'Documents', description: 'Access and manage related documents', href: '/documents' },
                        { title: 'Payments', description: 'Handle payment processing', href: '/payments' },
                    ]"
                    :key="index"
                    :href="item.href"
                    class="rounded-lg border bg-card p-6 hover:bg-muted/50"
                >
                    &lt;h2 class="mb-2 text-xl font-semibold">{{ item.title }}&lt;/h2>
                    &lt;p class="text-muted-foreground">{{ item.description }}&lt;/p>
                &lt;/Link>
            &lt;/div>
        &lt;/div>
    &lt;/AppLayout>
&lt;/template>
