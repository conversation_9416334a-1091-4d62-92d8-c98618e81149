<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Download, Upload, FileText, Eye } from 'lucide-vue-next';

interface Document {
    id: number;
    document_type: string;
    document_path: string;
    uploaded_image_path: string | null;
    created_at: string;
    supply: {
        id: number;
        supply_type: string;
        supplied_amount: number;
        poReceived: {
            po_number: string;
            institution_name: string;
            currency: string;
        };
        user: {
            name: string;
        };
    };
}

defineProps<{
    documents: Document[];
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Documents',
        href: '/documents',
    },
];

const uploadForm = useForm({
    image: null as File | null,
});

const uploadImage = (documentId: number, event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        uploadForm.image = target.files[0];
        uploadForm.post(`/documents/${documentId}/upload-image`, {
            onSuccess: () => {
                uploadForm.reset();
            },
        });
    }
};

const getDocumentTypeColor = (type: string) => {
    switch (type) {
        case 'dc':
            return 'bg-blue-100 text-blue-800';
        case 'invoice':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head title="Documents" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div>
                <h1 class="text-3xl font-bold">Document Management</h1>
                <p class="text-muted-foreground">View and manage DC and Invoice documents</p>
            </div>

            <div class="rounded-lg border bg-card">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-3 px-4 font-semibold">Document Type</th>
                                    <th class="text-left py-3 px-4 font-semibold">PO Number</th>
                                    <th class="text-left py-3 px-4 font-semibold">Institution</th>
                                    <th class="text-left py-3 px-4 font-semibold">Supply Amount</th>
                                    <th class="text-left py-3 px-4 font-semibold">Generated By</th>
                                    <th class="text-left py-3 px-4 font-semibold">Date</th>
                                    <th class="text-left py-3 px-4 font-semibold">Image Upload</th>
                                    <th class="text-left py-3 px-4 font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="document in documents" :key="document.id" class="border-b hover:bg-muted/50">
                                    <td class="py-3 px-4">
                                        <span 
                                            :class="getDocumentTypeColor(document.document_type)"
                                            class="px-2 py-1 rounded-full text-xs font-medium"
                                        >
                                            {{ document.document_type.toUpperCase() }}
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 font-medium">{{ document.supply.poReceived.po_number }}</td>
                                    <td class="py-3 px-4">{{ document.supply.poReceived.institution_name }}</td>
                                    <td class="py-3 px-4">
                                        {{ document.supply.poReceived.currency }} {{ Number(document.supply.supplied_amount).toLocaleString() }}
                                    </td>
                                    <td class="py-3 px-4">{{ document.supply.user.name }}</td>
                                    <td class="py-3 px-4">{{ new Date(document.created_at).toLocaleDateString() }}</td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-2">
                                            <div v-if="document.uploaded_image_path" class="flex items-center gap-2">
                                                <span class="text-green-600 text-sm">✓ Uploaded</span>
                                                <a :href="`/storage/${document.uploaded_image_path}`" target="_blank">
                                                    <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                        <Eye class="h-3 w-3" />
                                                        View
                                                    </Button>
                                                </a>
                                            </div>
                                            <div v-else>
                                                <label :for="`upload-${document.id}`" class="cursor-pointer">
                                                    <Button variant="outline" size="sm" class="flex items-center gap-1" as="span">
                                                        <Upload class="h-3 w-3" />
                                                        Upload
                                                    </Button>
                                                </label>
                                                <input
                                                    :id="`upload-${document.id}`"
                                                    type="file"
                                                    accept="image/*"
                                                    class="hidden"
                                                    @change="uploadImage(document.id, $event)"
                                                />
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-2">
                                            <Link :href="`/documents/${document.id}`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Eye class="h-3 w-3" />
                                                    View
                                                </Button>
                                            </Link>
                                            <a :href="`/documents/${document.id}/download`" target="_blank">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Download class="h-3 w-3" />
                                                    Download
                                                </Button>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div v-if="documents.length === 0" class="text-center py-8 text-muted-foreground">
                            No documents found. Documents are generated when processing supplies.
                            <Link href="/supply/create" class="text-primary hover:underline ml-1">Process a supply</Link>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-blue-600 mb-2">Delivery Challans</h3>
                    <p class="text-3xl font-bold">{{ documents.filter(d => d.document_type === 'dc').length }}</p>
                    <p class="text-sm text-muted-foreground">Total DC documents</p>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-green-600 mb-2">Invoices</h3>
                    <p class="text-3xl font-bold">{{ documents.filter(d => d.document_type === 'invoice').length }}</p>
                    <p class="text-sm text-muted-foreground">Total invoice documents</p>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-purple-600 mb-2">With Images</h3>
                    <p class="text-3xl font-bold">{{ documents.filter(d => d.uploaded_image_path).length }}</p>
                    <p class="text-sm text-muted-foreground">Documents with uploaded images</p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
