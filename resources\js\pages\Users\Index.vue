&lt;script setup lang="ts">
import { Head } from '@inertiajs/vue3'

defineProps<{
  users?: Array<{
    id: number
    name: string
    email: string
  }>
}>()
&lt;/script>

&lt;template>
  &lt;div>
    &lt;Head title="Users" />

    &lt;div class="py-12">
      &lt;div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        &lt;div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          &lt;div class="p-6 text-gray-900">
            &lt;h1 class="text-2xl font-semibold mb-6">Users&lt;/h1>
            
            &lt;div v-if="users?.length" class="overflow-x-auto">
              &lt;table class="min-w-full divide-y divide-gray-200">
                &lt;thead class="bg-gray-50">
                  &lt;tr>
                    &lt;th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name&lt;/th>
                    &lt;th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email&lt;/th>
                    &lt;th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions&lt;/th>
                  &lt;/tr>
                &lt;/thead>
                &lt;tbody class="bg-white divide-y divide-gray-200">
                  &lt;tr v-for="user in users" :key="user.id">
                    &lt;td class="px-6 py-4 whitespace-nowrap">{{ user.name }}&lt;/td>
                    &lt;td class="px-6 py-4 whitespace-nowrap">{{ user.email }}&lt;/td>
                    &lt;td class="px-6 py-4 whitespace-nowrap">
                      &lt;!-- Add your action buttons here -->
                    &lt;/td>
                  &lt;/tr>
                &lt;/tbody>
              &lt;/table>
            &lt;/div>
            &lt;p v-else class="text-gray-500">No users found.&lt;/p>
          &lt;/div>
        &lt;/div>
      &lt;/div>
    &lt;/div>
  &lt;/div>
&lt;/template>
