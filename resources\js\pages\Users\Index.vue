<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Plus, Edit, Trash2 } from 'lucide-vue-next';

interface User {
    id: number;
    name: string;
    email: string;
    role: string;
    created_at: string;
}

defineProps<{
    users: User[];
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Users',
        href: '/users',
    },
];

const deleteUser = (userId: number) => {
    if (confirm('Are you sure you want to delete this user?')) {
        router.delete(`/users/${userId}`);
    }
};
</script>

<template>
    <Head title="Users" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">User Management</h1>
                    <p class="text-muted-foreground">Manage system users and their roles</p>
                </div>
                <Link href="/users/create">
                    <Button class="flex items-center gap-2">
                        <Plus class="h-4 w-4" />
                        Add User
                    </Button>
                </Link>
            </div>

            <div class="rounded-lg border bg-card">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-3 px-4 font-semibold">Name</th>
                                    <th class="text-left py-3 px-4 font-semibold">Email</th>
                                    <th class="text-left py-3 px-4 font-semibold">Role</th>
                                    <th class="text-left py-3 px-4 font-semibold">Created</th>
                                    <th class="text-left py-3 px-4 font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="user in users" :key="user.id" class="border-b hover:bg-muted/50">
                                    <td class="py-3 px-4">{{ user.name }}</td>
                                    <td class="py-3 px-4">{{ user.email }}</td>
                                    <td class="py-3 px-4">
                                        <span
                                            :class="user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'"
                                            class="px-2 py-1 rounded-full text-xs font-medium"
                                        >
                                            {{ user.role }}
                                        </span>
                                    </td>
                                    <td class="py-3 px-4">
                                        {{ new Date(user.created_at).toLocaleDateString() }}
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-2">
                                            <Link :href="`/users/${user.id}/edit`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Edit class="h-3 w-3" />
                                                    Edit
                                                </Button>
                                            </Link>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                class="flex items-center gap-1 text-red-600 hover:text-red-700"
                                                @click="deleteUser(user.id)"
                                            >
                                                <Trash2 class="h-3 w-3" />
                                                Delete
                                            </Button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <div v-if="users.length === 0" class="text-center py-8 text-muted-foreground">
                            No users found. <Link href="/users/create" class="text-primary hover:underline">Create the first user</Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
