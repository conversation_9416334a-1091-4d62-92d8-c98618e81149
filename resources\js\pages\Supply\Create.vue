<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import { ArrowLeft } from 'lucide-vue-next';
import { ref, computed } from 'vue';

interface PoReceived {
    id: number;
    po_number: string;
    po_date: string;
    institution_name: string;
    grand_total: number;
    currency: string;
    status: string;
    items: Array<{
        id: number;
        product_name: string;
        quantity: number;
        price: number;
        total: number;
    }>;
}

const props = defineProps<{
    poReceived: PoReceived[];
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Supply',
        href: '/supply',
    },
    {
        title: 'Process Supply',
        href: '/supply/create',
    },
];

const selectedPoId = ref<number | null>(null);
const selectedPo = computed(() => {
    return props.poReceived.find(po => po.id === selectedPoId.value) || null;
});

const form = useForm({
    po_received_id: null as number | null,
    supply_type: 'partial',
    supplied_amount: 0,
    notes: '',
    generate_documents: true,
});

const selectPo = (poId: number) => {
    selectedPoId.value = poId;
    form.po_received_id = poId;
    form.supplied_amount = 0;
};

const setFullAmount = () => {
    if (selectedPo.value) {
        form.supplied_amount = selectedPo.value.grand_total;
        form.supply_type = 'full';
    }
};

const submit = () => {
    form.post('/supply', {
        onSuccess: () => {
            // Redirect will be handled by the controller
        },
    });
};
</script>

<template>
    <Head title="Process Supply" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center gap-4">
                <a href="/supply" class="flex items-center gap-2 text-muted-foreground hover:text-foreground">
                    <ArrowLeft class="h-4 w-4" />
                    Back to Supply
                </a>
            </div>

            <div>
                <h1 class="text-3xl font-bold">Process Supply</h1>
                <p class="text-muted-foreground">Select a PO and process partial or full supply</p>
            </div>

            <div class="max-w-4xl space-y-6">
                <!-- PO Selection -->
                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Select Purchase Order</h2>
                    
                    <div v-if="poReceived.length === 0" class="text-center py-8 text-muted-foreground">
                        No pending purchase orders available for supply.
                        <a href="/po-received/create" class="text-primary hover:underline ml-1">Create a new PO</a>
                    </div>

                    <div v-else class="grid gap-4">
                        <div 
                            v-for="po in poReceived" 
                            :key="po.id"
                            class="border rounded-lg p-4 cursor-pointer transition-colors"
                            :class="selectedPoId === po.id ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'"
                            @click="selectPo(po.id)"
                        >
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-semibold">{{ po.po_number }}</h3>
                                    <p class="text-sm text-muted-foreground">{{ po.institution_name }}</p>
                                    <p class="text-sm text-muted-foreground">
                                        Date: {{ new Date(po.po_date).toLocaleDateString() }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold">{{ po.currency }} {{ Number(po.grand_total).toLocaleString() }}</p>
                                    <span 
                                        :class="po.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'"
                                        class="px-2 py-1 rounded-full text-xs font-medium"
                                    >
                                        {{ po.status.replace('_', ' ').toUpperCase() }}
                                    </span>
                                </div>
                            </div>
                            
                            <div v-if="po.items.length > 0" class="mt-3 pt-3 border-t">
                                <p class="text-sm font-medium mb-2">Items ({{ po.items.length }}):</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <div v-for="item in po.items.slice(0, 4)" :key="item.id" class="text-sm text-muted-foreground">
                                        {{ item.product_name }} ({{ item.quantity }})
                                    </div>
                                    <div v-if="po.items.length > 4" class="text-sm text-muted-foreground">
                                        ... and {{ po.items.length - 4 }} more items
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Supply Details -->
                <div v-if="selectedPo" class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Supply Details</h2>
                    
                    <form @submit.prevent="submit" class="space-y-6">
                        <div class="grid gap-4">
                            <div class="grid gap-2">
                                <Label for="supply_type">Supply Type</Label>
                                <select
                                    id="supply_type"
                                    v-model="form.supply_type"
                                    class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                                    required
                                >
                                    <option value="partial">Partial Supply</option>
                                    <option value="full">Full Supply</option>
                                </select>
                                <InputError :message="form.errors.supply_type" />
                            </div>

                            <div class="grid gap-2">
                                <Label for="supplied_amount">Supplied Amount ({{ selectedPo.currency }})</Label>
                                <div class="flex gap-2">
                                    <Input
                                        id="supplied_amount"
                                        v-model.number="form.supplied_amount"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        :max="selectedPo.grand_total"
                                        placeholder="0.00"
                                        required
                                    />
                                    <Button type="button" variant="outline" @click="setFullAmount">
                                        Full Amount
                                    </Button>
                                </div>
                                <InputError :message="form.errors.supplied_amount" />
                                <p class="text-sm text-muted-foreground">
                                    Maximum: {{ selectedPo.currency }} {{ Number(selectedPo.grand_total).toLocaleString() }}
                                </p>
                            </div>

                            <div class="grid gap-2">
                                <Label for="notes">Notes (Optional)</Label>
                                <textarea
                                    id="notes"
                                    v-model="form.notes"
                                    class="flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                                    placeholder="Enter any additional notes about this supply..."
                                ></textarea>
                                <InputError :message="form.errors.notes" />
                            </div>

                            <div class="flex items-center space-x-2">
                                <input
                                    id="generate_documents"
                                    v-model="form.generate_documents"
                                    type="checkbox"
                                    class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                />
                                <Label for="generate_documents" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                    Generate DC and Invoice documents
                                </Label>
                            </div>
                        </div>

                        <div class="flex items-center gap-4">
                            <Button type="submit" :disabled="form.processing">
                                {{ form.processing ? 'Processing...' : 'Process Supply' }}
                            </Button>
                            <a href="/supply">
                                <Button type="button" variant="outline">
                                    Cancel
                                </Button>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
