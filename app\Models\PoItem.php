<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PoItem extends Model
{
    protected $fillable = [
        'po_received_id',
        'product_name',
        'price',
        'quantity',
        'batch_no',
        'mfg_date',
        'expiry_date',
        'total',
        'supplied_quantity',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'total' => 'decimal:2',
        'mfg_date' => 'date',
        'expiry_date' => 'date',
    ];

    public function poReceived(): BelongsTo
    {
        return $this->belongsTo(PoReceived::class);
    }

    public function getRemainingQuantityAttribute()
    {
        return $this->quantity - $this->supplied_quantity;
    }
}
