&lt;script setup lang="ts">
import AppLayout from '@/layouts/app/AppSidebarLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'user',
});

const breadcrumbs = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Users',
        href: '/users',
    },
    {
        title: 'Create User',
        href: '/users/create',
    },
];

const submit = () => {
    form.post('/users');
};
&lt;/script>

&lt;template>
    &lt;Head title="Create User" />

    &lt;AppLayout :breadcrumbs="breadcrumbs">
        &lt;div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            &lt;div>
                &lt;h1 class="text-3xl font-bold">Create User&lt;/h1>
                &lt;p class="text-muted-foreground">Add a new user to the system&lt;/p>
            &lt;/div>

            &lt;div class="rounded-lg border bg-card">
                &lt;form @submit.prevent="submit" class="p-6">
                    &lt;div class="space-y-6">
                        &lt;div>
                            &lt;label for="name" class="block text-sm font-medium text-foreground">Name&lt;/label>
                            &lt;input
                                id="name"
                                v-model="form.name"
                                type="text"
                                class="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2"
                                required
                            />
                            &lt;div v-if="form.errors.name" class="mt-1 text-sm text-red-600">{{ form.errors.name }}&lt;/div>
                        &lt;/div>

                        &lt;div>
                            &lt;label for="email" class="block text-sm font-medium text-foreground">Email&lt;/label>
                            &lt;input
                                id="email"
                                v-model="form.email"
                                type="email"
                                class="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2"
                                required
                            />
                            &lt;div v-if="form.errors.email" class="mt-1 text-sm text-red-600">{{ form.errors.email }}&lt;/div>
                        &lt;/div>

                        &lt;div>
                            &lt;label for="password" class="block text-sm font-medium text-foreground">Password&lt;/label>
                            &lt;input
                                id="password"
                                v-model="form.password"
                                type="password"
                                class="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2"
                                required
                            />
                            &lt;div v-if="form.errors.password" class="mt-1 text-sm text-red-600">{{ form.errors.password }}&lt;/div>
                        &lt;/div>

                        &lt;div>
                            &lt;label for="password_confirmation" class="block text-sm font-medium text-foreground">
                                Confirm Password
                            &lt;/label>
                            &lt;input
                                id="password_confirmation"
                                v-model="form.password_confirmation"
                                type="password"
                                class="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2"
                                required
                            />
                        &lt;/div>

                        &lt;div>
                            &lt;label for="role" class="block text-sm font-medium text-foreground">Role&lt;/label>
                            &lt;select
                                id="role"
                                v-model="form.role"
                                class="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2"
                                required
                            >
                                &lt;option value="user">User&lt;/option>
                                &lt;option value="admin">Admin&lt;/option>
                            &lt;/select>
                            &lt;div v-if="form.errors.role" class="mt-1 text-sm text-red-600">{{ form.errors.role }}&lt;/div>
                        &lt;/div>

                        &lt;div class="flex justify-end">
                            &lt;button
                                type="submit"
                                class="rounded-md bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90"
                                :disabled="form.processing"
                            >
                                Create User
                            &lt;/button>
                        &lt;/div>
                    &lt;/div>
                &lt;/form>
            &lt;/div>
        &lt;/div>
    &lt;/AppLayout>
&lt;/template>
