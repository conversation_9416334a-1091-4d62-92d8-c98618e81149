<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PoReceived extends Model
{
    protected $table = 'po_received';

    protected $fillable = [
        'user_id',
        'po_number',
        'po_date',
        'po_image',
        'institution_name',
        'email',
        'phone',
        'address',
        'grand_total',
        'currency',
        'status',
    ];

    protected $casts = [
        'po_date' => 'date',
        'grand_total' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(PoItem::class);
    }

    public function supplies(): HasMany
    {
        return $this->hasMany(Supply::class);
    }

    public function getTotalSuppliedAmountAttribute()
    {
        return $this->supplies->sum('supplied_amount');
    }

    public function getRemainingAmountAttribute()
    {
        return $this->grand_total - $this->getTotalSuppliedAmountAttribute();
    }
}
