<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .document-title {
            font-size: 18px;
            font-weight: bold;
            color: #666;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
        }
        .billing-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .billing-box {
            width: 45%;
        }
        .billing-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .table .text-right {
            text-align: right;
        }
        .total-section {
            text-align: right;
            margin-top: 20px;
        }
        .total-row {
            margin-bottom: 5px;
        }
        .total-amount {
            font-size: 16px;
            font-weight: bold;
            border-top: 2px solid #333;
            padding-top: 10px;
        }
        .footer {
            margin-top: 40px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .payment-terms {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border-left: 4px solid #333;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">Supply Management System</div>
        <div class="document-title">INVOICE</div>
    </div>

    <div class="info-section">
        <div class="info-row">
            <span class="info-label">Invoice Number:</span>
            <span>INV-{{ $supply->id }}-{{ date('Y') }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Invoice Date:</span>
            <span>{{ $supply->created_at->format('d/m/Y') }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">PO Number:</span>
            <span>{{ $supply->poReceived->po_number }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">PO Date:</span>
            <span>{{ $supply->poReceived->po_date->format('d/m/Y') }}</span>
        </div>
    </div>

    <div class="billing-info">
        <div class="billing-box">
            <div class="billing-title">Bill To:</div>
            <div><strong>{{ $supply->poReceived->institution_name }}</strong></div>
            <div>{{ $supply->poReceived->address }}</div>
            <div>Email: {{ $supply->poReceived->email }}</div>
            <div>Phone: {{ $supply->poReceived->phone }}</div>
        </div>
        <div class="billing-box">
            <div class="billing-title">Supplied By:</div>
            <div><strong>{{ $supply->user->name }}</strong></div>
            <div>Supply Type: {{ ucfirst($supply->supply_type) }}</div>
            <div>Supply Date: {{ $supply->created_at->format('d/m/Y') }}</div>
        </div>
    </div>

    <table class="table">
        <thead>
            <tr>
                <th>S.No</th>
                <th>Product Name</th>
                <th>Batch No</th>
                <th>Quantity</th>
                <th class="text-right">Unit Price (PKR)</th>
                <th class="text-right">Total (PKR)</th>
            </tr>
        </thead>
        <tbody>
            @foreach($supply->poReceived->items as $index => $item)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $item->product_name }}</td>
                <td>{{ $item->batch_no }}</td>
                <td>{{ $item->quantity }}</td>
                <td class="text-right">{{ number_format($item->price, 2) }}</td>
                <td class="text-right">{{ number_format($item->total, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="total-section">
        <div class="total-row">
            <strong>Subtotal: PKR {{ number_format($supply->poReceived->grand_total, 2) }}</strong>
        </div>
        <div class="total-row">
            Tax: PKR 0.00
        </div>
        <div class="total-amount">
            Supplied Amount: PKR {{ number_format($supply->supplied_amount, 2) }}
        </div>
    </div>

    @if($supply->notes)
    <div class="info-section">
        <div class="info-row">
            <span class="info-label">Notes:</span>
            <span>{{ $supply->notes }}</span>
        </div>
    </div>
    @endif

    <div class="payment-terms">
        <strong>Payment Terms:</strong><br>
        Payment is due within 30 days of invoice date.<br>
        Please include invoice number with payment.
    </div>

    <div class="footer">
        <p><strong>Generated by:</strong> {{ $supply->user->name }}</p>
        <p><strong>Generated on:</strong> {{ now()->format('d/m/Y H:i:s') }}</p>
        <p><strong>Status:</strong> {{ ucfirst($supply->poReceived->status) }}</p>
    </div>
</body>
</html>
