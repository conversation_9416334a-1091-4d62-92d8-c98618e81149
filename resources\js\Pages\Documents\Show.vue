<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Download, Upload, Eye } from 'lucide-vue-next';

interface Document {
    id: number;
    document_type: string;
    document_path: string;
    uploaded_image_path: string | null;
    created_at: string;
    supply: {
        id: number;
        supply_type: string;
        supplied_amount: number;
        created_at: string;
        poReceived: {
            id: number;
            po_number: string;
            po_date: string;
            institution_name: string;
            email: string;
            phone: string;
            address: string;
            grand_total: number;
            currency: string;
            status: string;
        };
        user: {
            name: string;
            email: string;
        };
    };
}

const props = defineProps<{
    document: Document;
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Documents',
        href: '/documents',
    },
    {
        title: `${props.document.document_type.toUpperCase()} #${props.document.id}`,
        href: `/documents/${props.document.id}`,
    },
];

const uploadForm = useForm({
    image: null as File | null,
});

const uploadImage = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        uploadForm.image = target.files[0];
        uploadForm.post(`/documents/${props.document.id}/upload-image`, {
            onSuccess: () => {
                uploadForm.reset();
            },
        });
    }
};

const getDocumentTypeColor = (type: string) => {
    switch (type) {
        case 'dc':
            return 'bg-blue-100 text-blue-800';
        case 'invoice':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head :title="`${document.document_type.toUpperCase()} #${document.id}`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <a href="/documents" class="flex items-center gap-2 text-muted-foreground hover:text-foreground">
                        <ArrowLeft class="h-4 w-4" />
                        Back to Documents
                    </a>
                </div>
                <div class="flex items-center gap-2">
                    <a :href="`/documents/${document.id}/download`" target="_blank">
                        <Button class="flex items-center gap-2">
                            <Download class="h-4 w-4" />
                            Download PDF
                        </Button>
                    </a>
                </div>
            </div>

            <div>
                <h1 class="text-3xl font-bold">{{ document.document_type.toUpperCase() }} #{{ document.id }}</h1>
                <div class="flex items-center gap-4 mt-2">
                    <span 
                        :class="getDocumentTypeColor(document.document_type)"
                        class="px-3 py-1 rounded-full text-sm font-medium"
                    >
                        {{ document.document_type.toUpperCase() }}
                    </span>
                    <span class="text-muted-foreground">
                        Generated on {{ new Date(document.created_at).toLocaleDateString() }}
                    </span>
                </div>
            </div>

            <!-- Document Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Document Details</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">Document Type:</span>
                            <span>{{ document.document_type.toUpperCase() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Generated Date:</span>
                            <span>{{ new Date(document.created_at).toLocaleDateString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Supply ID:</span>
                            <Link :href="`/supply/${document.supply.id}`" class="text-blue-600 hover:underline">
                                #{{ document.supply.id }}
                            </Link>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Supply Amount:</span>
                            <span>{{ document.supply.poReceived.currency }} {{ Number(document.supply.supplied_amount).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Generated By:</span>
                            <span>{{ document.supply.user.name }}</span>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Related PO Information</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">PO Number:</span>
                            <Link :href="`/po-received/${document.supply.poReceived.id}`" class="text-blue-600 hover:underline">
                                {{ document.supply.poReceived.po_number }}
                            </Link>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Institution:</span>
                            <span>{{ document.supply.poReceived.institution_name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">PO Date:</span>
                            <span>{{ new Date(document.supply.poReceived.po_date).toLocaleDateString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Total Amount:</span>
                            <span>{{ document.supply.poReceived.currency }} {{ Number(document.supply.poReceived.grand_total).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Status:</span>
                            <span>{{ document.supply.poReceived.status.replace('_', ' ').toUpperCase() }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Institution Contact Information -->
            <div class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4">Institution Contact Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium mb-2">Contact Details</h3>
                        <div class="space-y-2 text-sm">
                            <p><span class="font-medium">Email:</span> {{ document.supply.poReceived.email }}</p>
                            <p><span class="font-medium">Phone:</span> {{ document.supply.poReceived.phone }}</p>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-medium mb-2">Address</h3>
                        <p class="text-sm">{{ document.supply.poReceived.address }}</p>
                    </div>
                </div>
            </div>

            <!-- Image Upload Section -->
            <div class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4">Document Image</h2>
                
                <div v-if="document.uploaded_image_path" class="space-y-4">
                    <div class="flex items-center justify-between">
                        <p class="text-green-600 font-medium">✓ Image uploaded successfully</p>
                        <a :href="`/storage/${document.uploaded_image_path}`" target="_blank">
                            <Button variant="outline" class="flex items-center gap-2">
                                <Eye class="h-4 w-4" />
                                View Image
                            </Button>
                        </a>
                    </div>
                    
                    <div class="border rounded-lg p-4">
                        <img 
                            :src="`/storage/${document.uploaded_image_path}`" 
                            :alt="`${document.document_type} image`"
                            class="max-w-full h-auto max-h-96 mx-auto rounded-lg shadow-sm"
                        />
                    </div>

                    <div class="border-t pt-4">
                        <p class="text-sm text-muted-foreground mb-3">Replace with a new image:</p>
                        <div class="flex items-center gap-4">
                            <label for="image-upload" class="cursor-pointer inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground">
                                <Upload class="h-4 w-4" />
                                Upload New Image
                            </label>
                            <input
                                id="image-upload"
                                type="file"
                                accept="image/*"
                                class="hidden"
                                @change="uploadImage"
                            />
                        </div>
                    </div>
                </div>

                <div v-else class="text-center py-8">
                    <p class="text-muted-foreground mb-4">No image uploaded for this document</p>
                    <div class="flex items-center justify-center gap-4">
                        <label for="image-upload" class="cursor-pointer inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground">
                            <Upload class="h-4 w-4" />
                            Upload Image
                        </label>
                        <input
                            id="image-upload"
                            type="file"
                            accept="image/*"
                            class="hidden"
                            @change="uploadImage"
                        />
                    </div>
                    <p class="text-sm text-muted-foreground mt-2">
                        Upload a scanned copy or photo of the physical document
                    </p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
