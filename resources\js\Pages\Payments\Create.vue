<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import { ArrowLeft } from 'lucide-vue-next';

interface Supply {
    id: number;
    supply_type: string;
    supplied_amount: number;
    created_at: string;
    poReceived: {
        id: number;
        po_number: string;
        institution_name: string;
        currency: string;
        grand_total: number;
    };
    user: {
        name: string;
    };
}

defineProps<{
    supplies: Supply[];
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Payments',
        href: '/payments',
    },
    {
        title: 'Process Payment',
        href: '/payments/create',
    },
];

const form = useForm({
    supply_id: '',
    amount: 0,
    cheque_number: '',
    cheque_date: '',
    cheque_image: null as File | null,
    total_amount: 0,
});

const selectedSupply = computed(() => {
    if (!form.supply_id) return null;
    return supplies.find(supply => supply.id.toString() === form.supply_id);
});

const updateAmount = () => {
    if (selectedSupply.value) {
        form.amount = selectedSupply.value.supplied_amount;
        form.total_amount = selectedSupply.value.supplied_amount;
    }
};

const submit = () => {
    form.post('/payments', {
        forceFormData: true,
        onSuccess: () => {
            // Redirect will be handled by the controller
        },
    });
};

const handleFileChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        form.cheque_image = target.files[0];
    }
};

watch(() => form.supply_id, updateAmount);
</script>

<template>
    <Head title="Process Payment" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center gap-4">
                <a href="/payments" class="flex items-center gap-2 text-muted-foreground hover:text-foreground">
                    <ArrowLeft class="h-4 w-4" />
                    Back to Payments
                </a>
            </div>

            <div>
                <h1 class="text-3xl font-bold">Process Payment</h1>
                <p class="text-muted-foreground">Process payment for a supply</p>
            </div>

            <div class="max-w-2xl">
                <form @submit.prevent="submit" class="space-y-6">
                    <!-- Supply Selection -->
                    <div class="rounded-lg border bg-card p-6">
                        <h2 class="text-xl font-semibold mb-4">Select Supply</h2>
                        <div class="grid gap-4">
                            <div class="grid gap-2">
                                <Label for="supply_id">Supply</Label>
                                <select
                                    id="supply_id"
                                    v-model="form.supply_id"
                                    class="flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                                    required
                                >
                                    <option value="">Select a supply</option>
                                    <option
                                        v-for="supply in props.supplies"
                                        :key="supply.id"
                                        :value="supply.id"
                                    >
                                        {{ supply.poReceived.po_number }} - {{ supply.poReceived.institution_name }} ({{ supply.poReceived.currency }} {{ Number(supply.supplied_amount).toLocaleString() }})
                                    </option>
                                </select>
                                <InputError :message="form.errors.supply_id" />
                            </div>

                            <div v-if="selectedSupply" class="rounded-lg bg-muted/50 p-4">
                                <h3 class="font-medium mb-2">Supply Details</h3>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium">PO Number:</span>
                                        <p>{{ selectedSupply.poReceived.po_number }}</p>
                                    </div>
                                    <div>
                                        <span class="font-medium">Institution:</span>
                                        <p>{{ selectedSupply.poReceived.institution_name }}</p>
                                    </div>
                                    <div>
                                        <span class="font-medium">Supply Type:</span>
                                        <p>{{ selectedSupply.supply_type.toUpperCase() }}</p>
                                    </div>
                                    <div>
                                        <span class="font-medium">Supplied Amount:</span>
                                        <p>{{ selectedSupply.poReceived.currency }} {{ Number(selectedSupply.supplied_amount).toLocaleString() }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details -->
                    <div class="rounded-lg border bg-card p-6">
                        <h2 class="text-xl font-semibold mb-4">Payment Details</h2>
                        <div class="grid gap-4">
                            <div class="grid gap-2">
                                <Label for="amount">Payment Amount ({{ selectedSupply?.poReceived.currency || 'PKR' }})</Label>
                                <Input
                                    id="amount"
                                    v-model.number="form.amount"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="0.00"
                                    required
                                />
                                <InputError :message="form.errors.amount" />
                            </div>

                            <div class="grid gap-2">
                                <Label for="total_amount">Total Amount ({{ selectedSupply?.poReceived.currency || 'PKR' }})</Label>
                                <Input
                                    id="total_amount"
                                    v-model.number="form.total_amount"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="0.00"
                                    required
                                />
                                <InputError :message="form.errors.total_amount" />
                                <p class="text-sm text-muted-foreground">
                                    Total amount including any additional charges or deductions
                                </p>
                            </div>

                            <div class="grid gap-2">
                                <Label for="cheque_number">Cheque Number</Label>
                                <Input
                                    id="cheque_number"
                                    v-model="form.cheque_number"
                                    type="text"
                                    placeholder="Enter cheque number"
                                    required
                                />
                                <InputError :message="form.errors.cheque_number" />
                            </div>

                            <div class="grid gap-2">
                                <Label for="cheque_date">Cheque Date</Label>
                                <Input
                                    id="cheque_date"
                                    v-model="form.cheque_date"
                                    type="date"
                                    required
                                />
                                <InputError :message="form.errors.cheque_date" />
                            </div>

                            <div class="grid gap-2">
                                <Label for="cheque_image">Cheque Image (Optional)</Label>
                                <Input
                                    id="cheque_image"
                                    type="file"
                                    accept="image/*"
                                    @change="handleFileChange"
                                />
                                <InputError :message="form.errors.cheque_image" />
                                <p class="text-sm text-muted-foreground">
                                    Upload a photo or scan of the cheque
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center gap-4">
                        <Button type="submit" :disabled="form.processing || !selectedSupply">
                            {{ form.processing ? 'Processing...' : 'Process Payment' }}
                        </Button>
                        <a href="/payments">
                            <Button type="button" variant="outline">
                                Cancel
                            </Button>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>
