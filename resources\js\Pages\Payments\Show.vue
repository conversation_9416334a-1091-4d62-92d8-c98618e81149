<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Eye } from 'lucide-vue-next';

interface Payment {
    id: number;
    amount: number;
    cheque_number: string;
    cheque_date: string;
    cheque_image: string | null;
    total_amount: number;
    status: string;
    created_at: string;
    supply: {
        id: number;
        supply_type: string;
        supplied_amount: number;
        created_at: string;
        poReceived: {
            id: number;
            po_number: string;
            po_date: string;
            institution_name: string;
            email: string;
            phone: string;
            address: string;
            grand_total: number;
            currency: string;
            status: string;
        };
        user: {
            name: string;
        };
    };
    user: {
        name: string;
        email: string;
    };
}

const props = defineProps<{
    payment: Payment;
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Payments',
        href: '/payments',
    },
    {
        title: `Payment #${props.payment.id}`,
        href: `/payments/${props.payment.id}`,
    },
];

const updateStatus = (status: string) => {
    if (confirm(`Are you sure you want to mark this payment as ${status}?`)) {
        router.patch(`/payments/${props.payment.id}/status`, { status });
    }
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'cleared':
            return 'bg-green-100 text-green-800';
        case 'bounced':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head :title="`Payment #${payment.id}`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <a href="/payments" class="flex items-center gap-2 text-muted-foreground hover:text-foreground">
                        <ArrowLeft class="h-4 w-4" />
                        Back to Payments
                    </a>
                </div>
                <div class="flex items-center gap-2">
                    <div v-if="payment.status === 'pending'" class="flex gap-2">
                        <Button 
                            variant="outline" 
                            class="text-green-600 hover:text-green-700"
                            @click="updateStatus('cleared')"
                        >
                            Mark as Cleared
                        </Button>
                        <Button 
                            variant="outline" 
                            class="text-red-600 hover:text-red-700"
                            @click="updateStatus('bounced')"
                        >
                            Mark as Bounced
                        </Button>
                    </div>
                </div>
            </div>

            <div>
                <h1 class="text-3xl font-bold">Payment #{{ payment.id }}</h1>
                <div class="flex items-center gap-4 mt-2">
                    <span 
                        :class="getStatusColor(payment.status)"
                        class="px-3 py-1 rounded-full text-sm font-medium"
                    >
                        {{ payment.status.toUpperCase() }}
                    </span>
                    <span class="text-muted-foreground">
                        Processed by {{ payment.user.name }} on {{ new Date(payment.created_at).toLocaleDateString() }}
                    </span>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Payment Details</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">Payment Amount:</span>
                            <span class="font-semibold text-lg">{{ payment.supply.poReceived.currency }} {{ Number(payment.amount).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Total Amount:</span>
                            <span>{{ payment.supply.poReceived.currency }} {{ Number(payment.total_amount).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Cheque Number:</span>
                            <span>{{ payment.cheque_number }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Cheque Date:</span>
                            <span>{{ new Date(payment.cheque_date).toLocaleDateString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Status:</span>
                            <span 
                                :class="getStatusColor(payment.status)"
                                class="px-2 py-1 rounded-full text-xs font-medium"
                            >
                                {{ payment.status.toUpperCase() }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Processed By:</span>
                            <span>{{ payment.user.name }}</span>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Related Supply Information</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">Supply ID:</span>
                            <Link :href="`/supply/${payment.supply.id}`" class="text-blue-600 hover:underline">
                                #{{ payment.supply.id }}
                            </Link>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Supply Type:</span>
                            <span>{{ payment.supply.supply_type.toUpperCase() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Supply Amount:</span>
                            <span>{{ payment.supply.poReceived.currency }} {{ Number(payment.supply.supplied_amount).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Supply Date:</span>
                            <span>{{ new Date(payment.supply.created_at).toLocaleDateString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Supplied By:</span>
                            <span>{{ payment.supply.user.name }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PO Information -->
            <div class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4">Purchase Order Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">PO Number:</span>
                            <Link :href="`/po-received/${payment.supply.poReceived.id}`" class="text-blue-600 hover:underline">
                                {{ payment.supply.poReceived.po_number }}
                            </Link>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Institution:</span>
                            <span>{{ payment.supply.poReceived.institution_name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">PO Date:</span>
                            <span>{{ new Date(payment.supply.poReceived.po_date).toLocaleDateString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">PO Total:</span>
                            <span>{{ payment.supply.poReceived.currency }} {{ Number(payment.supply.poReceived.grand_total).toLocaleString() }}</span>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <span class="font-medium">Contact Information:</span>
                            <div class="mt-1 text-sm space-y-1">
                                <p>Email: {{ payment.supply.poReceived.email }}</p>
                                <p>Phone: {{ payment.supply.poReceived.phone }}</p>
                            </div>
                        </div>
                        <div>
                            <span class="font-medium">Address:</span>
                            <p class="mt-1 text-sm">{{ payment.supply.poReceived.address }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cheque Image -->
            <div class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4">Cheque Image</h2>
                
                <div v-if="payment.cheque_image" class="space-y-4">
                    <div class="flex items-center justify-between">
                        <p class="text-green-600 font-medium">✓ Cheque image available</p>
                        <a :href="`/storage/${payment.cheque_image}`" target="_blank">
                            <Button variant="outline" class="flex items-center gap-2">
                                <Eye class="h-4 w-4" />
                                View Full Size
                            </Button>
                        </a>
                    </div>
                    
                    <div class="border rounded-lg p-4">
                        <img 
                            :src="`/storage/${payment.cheque_image}`" 
                            alt="Cheque image"
                            class="max-w-full h-auto max-h-96 mx-auto rounded-lg shadow-sm"
                        />
                    </div>
                </div>

                <div v-else class="text-center py-8 text-muted-foreground">
                    <p>No cheque image uploaded for this payment</p>
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="rounded-lg border bg-muted/50 p-6">
                <h2 class="text-xl font-semibold mb-4">Payment Summary</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-600">{{ payment.supply.poReceived.currency }} {{ Number(payment.amount).toLocaleString() }}</p>
                        <p class="text-sm text-muted-foreground">Payment Amount</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-green-600">{{ payment.supply.poReceived.currency }} {{ Number(payment.supply.supplied_amount).toLocaleString() }}</p>
                        <p class="text-sm text-muted-foreground">Supply Amount</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-purple-600">{{ Math.round((payment.amount / payment.supply.supplied_amount) * 100) }}%</p>
                        <p class="text-sm text-muted-foreground">Payment Coverage</p>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
