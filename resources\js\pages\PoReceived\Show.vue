<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, FileText } from 'lucide-vue-next';

interface PoReceived {
    id: number;
    po_number: string;
    po_date: string;
    po_image: string | null;
    institution_name: string;
    email: string;
    phone: string;
    address: string;
    grand_total: number;
    currency: string;
    status: string;
    created_at: string;
    user: {
        name: string;
        email: string;
    };
    items: Array<{
        id: number;
        product_name: string;
        price: number;
        quantity: number;
        batch_no: string;
        mfg_date: string;
        expiry_date: string;
        total: number;
        supplied_quantity: number;
    }>;
    supplies: Array<{
        id: number;
        supply_type: string;
        supplied_amount: number;
        created_at: string;
        user: {
            name: string;
        };
    }>;
}

const props = defineProps<{
    poReceived: PoReceived;
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'PO Received',
        href: '/po-received',
    },
    {
        title: props.poReceived.po_number,
        href: `/po-received/${props.poReceived.id}`,
    },
];

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'partially_supplied':
            return 'bg-blue-100 text-blue-800';
        case 'fully_supplied':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head :title="`PO ${poReceived.po_number}`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <a href="/po-received" class="flex items-center gap-2 text-muted-foreground hover:text-foreground">
                        <ArrowLeft class="h-4 w-4" />
                        Back to PO Received
                    </a>
                </div>
                <div class="flex items-center gap-2">
                    <Link :href="`/po-received/${poReceived.id}/edit`">
                        <Button class="flex items-center gap-2">
                            <Edit class="h-4 w-4" />
                            Edit PO
                        </Button>
                    </Link>
                </div>
            </div>

            <div>
                <h1 class="text-3xl font-bold">PO {{ poReceived.po_number }}</h1>
                <div class="flex items-center gap-4 mt-2">
                    <span 
                        :class="getStatusColor(poReceived.status)"
                        class="px-3 py-1 rounded-full text-sm font-medium"
                    >
                        {{ poReceived.status.replace('_', ' ').toUpperCase() }}
                    </span>
                    <span class="text-muted-foreground">
                        Created by {{ poReceived.user.name }} on {{ new Date(poReceived.created_at).toLocaleDateString() }}
                    </span>
                </div>
            </div>

            <!-- PO Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">PO Information</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">PO Number:</span>
                            <span>{{ poReceived.po_number }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">PO Date:</span>
                            <span>{{ new Date(poReceived.po_date).toLocaleDateString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Grand Total:</span>
                            <span class="font-semibold">{{ poReceived.currency }} {{ Number(poReceived.grand_total).toLocaleString() }}</span>
                        </div>
                        <div v-if="poReceived.po_image" class="flex justify-between">
                            <span class="font-medium">PO Image:</span>
                            <a :href="`/storage/${poReceived.po_image}`" target="_blank" class="text-blue-600 hover:underline">
                                View Image
                            </a>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Institution Information</h2>
                    <div class="space-y-3">
                        <div>
                            <span class="font-medium">Name:</span>
                            <p>{{ poReceived.institution_name }}</p>
                        </div>
                        <div>
                            <span class="font-medium">Email:</span>
                            <p>{{ poReceived.email }}</p>
                        </div>
                        <div>
                            <span class="font-medium">Phone:</span>
                            <p>{{ poReceived.phone }}</p>
                        </div>
                        <div>
                            <span class="font-medium">Address:</span>
                            <p>{{ poReceived.address }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Items -->
            <div class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4">Items</h2>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-3 px-4 font-semibold">Product</th>
                                <th class="text-left py-3 px-4 font-semibold">Batch No</th>
                                <th class="text-left py-3 px-4 font-semibold">Quantity</th>
                                <th class="text-left py-3 px-4 font-semibold">Price</th>
                                <th class="text-left py-3 px-4 font-semibold">Total</th>
                                <th class="text-left py-3 px-4 font-semibold">Mfg Date</th>
                                <th class="text-left py-3 px-4 font-semibold">Expiry Date</th>
                                <th class="text-left py-3 px-4 font-semibold">Supplied</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in poReceived.items" :key="item.id" class="border-b">
                                <td class="py-3 px-4">{{ item.product_name }}</td>
                                <td class="py-3 px-4">{{ item.batch_no }}</td>
                                <td class="py-3 px-4">{{ item.quantity }}</td>
                                <td class="py-3 px-4">{{ poReceived.currency }} {{ Number(item.price).toLocaleString() }}</td>
                                <td class="py-3 px-4">{{ poReceived.currency }} {{ Number(item.total).toLocaleString() }}</td>
                                <td class="py-3 px-4">{{ new Date(item.mfg_date).toLocaleDateString() }}</td>
                                <td class="py-3 px-4">{{ new Date(item.expiry_date).toLocaleDateString() }}</td>
                                <td class="py-3 px-4">{{ item.supplied_quantity }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Supply History -->
            <div v-if="poReceived.supplies.length > 0" class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4">Supply History</h2>
                <div class="space-y-4">
                    <div v-for="supply in poReceived.supplies" :key="supply.id" class="border rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium">{{ supply.supply_type.toUpperCase() }} Supply</p>
                                <p class="text-sm text-muted-foreground">
                                    {{ poReceived.currency }} {{ Number(supply.supplied_amount).toLocaleString() }} 
                                    by {{ supply.user.name }} on {{ new Date(supply.created_at).toLocaleDateString() }}
                                </p>
                            </div>
                            <Link :href="`/supply/${supply.id}`">
                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                    <FileText class="h-3 w-3" />
                                    View Details
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
