<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Plus, Eye, Edit, Package } from 'lucide-vue-next';

interface PoReceived {
    id: number;
    po_number: string;
    po_date: string;
    institution_name: string;
    email: string;
    phone: string;
    address: string;
    grand_total: number;
    currency: string;
    status: string;
    created_at: string;
    user: {
        name: string;
    };
    items_count: number;
    total_supplied_amount: number;
    remaining_amount: number;
}

defineProps<{
    poReceiveds: PoReceived[];
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'PO Received',
        href: '/po-received',
    },
];

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'partially_supplied':
            return 'bg-blue-100 text-blue-800';
        case 'fully_supplied':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head title="PO Received" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Purchase Orders</h1>
                    <p class="text-muted-foreground">Manage received purchase orders</p>
                </div>
                <Link href="/po-received/create">
                    <Button class="flex items-center gap-2">
                        <Plus class="h-4 w-4" />
                        Add PO
                    </Button>
                </Link>
            </div>

            <div class="rounded-lg border bg-card">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-3 px-4 font-semibold">PO Number</th>
                                    <th class="text-left py-3 px-4 font-semibold">Institution</th>
                                    <th class="text-left py-3 px-4 font-semibold">Total Amount</th>
                                    <th class="text-left py-3 px-4 font-semibold">Supplied</th>
                                    <th class="text-left py-3 px-4 font-semibold">Status</th>
                                    <th class="text-left py-3 px-4 font-semibold">Items</th>
                                    <th class="text-left py-3 px-4 font-semibold">Date</th>
                                    <th class="text-left py-3 px-4 font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="po in poReceiveds" :key="po.id" class="border-b hover:bg-muted/50">
                                    <td class="py-3 px-4 font-medium">{{ po.po_number }}</td>
                                    <td class="py-3 px-4">{{ po.institution_name }}</td>
                                    <td class="py-3 px-4">{{ po.currency }} {{ Number(po.grand_total).toLocaleString() }}</td>
                                    <td class="py-3 px-4">
                                        <div class="text-sm">
                                            <div>{{ po.currency }} {{ Number(po.total_supplied_amount).toLocaleString() }}</div>
                                            <div class="text-muted-foreground">
                                                Remaining: {{ po.currency }} {{ Number(po.remaining_amount).toLocaleString() }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span 
                                            :class="getStatusColor(po.status)"
                                            class="px-2 py-1 rounded-full text-xs font-medium"
                                        >
                                            {{ po.status.replace('_', ' ').toUpperCase() }}
                                        </span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-1">
                                            <Package class="h-4 w-4 text-muted-foreground" />
                                            <span>{{ po.items_count }}</span>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">{{ new Date(po.po_date).toLocaleDateString() }}</td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-2">
                                            <Link :href="`/po-received/${po.id}`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Eye class="h-3 w-3" />
                                                    View
                                                </Button>
                                            </Link>
                                            <Link :href="`/po-received/${po.id}/edit`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Edit class="h-3 w-3" />
                                                    Edit
                                                </Button>
                                            </Link>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div v-if="poReceiveds.length === 0" class="text-center py-8 text-muted-foreground">
                            No purchase orders found. <Link href="/po-received/create" class="text-primary hover:underline">Create the first PO</Link>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-blue-600 mb-2">Total POs</h3>
                    <p class="text-3xl font-bold">{{ poReceiveds.length }}</p>
                    <p class="text-sm text-muted-foreground">Purchase orders</p>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-yellow-600 mb-2">Pending</h3>
                    <p class="text-3xl font-bold">{{ poReceiveds.filter(po => po.status === 'pending').length }}</p>
                    <p class="text-sm text-muted-foreground">Awaiting supply</p>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-blue-600 mb-2">Partial</h3>
                    <p class="text-3xl font-bold">{{ poReceiveds.filter(po => po.status === 'partially_supplied').length }}</p>
                    <p class="text-sm text-muted-foreground">Partially supplied</p>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-green-600 mb-2">Complete</h3>
                    <p class="text-3xl font-bold">{{ poReceiveds.filter(po => po.status === 'fully_supplied').length }}</p>
                    <p class="text-sm text-muted-foreground">Fully supplied</p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
