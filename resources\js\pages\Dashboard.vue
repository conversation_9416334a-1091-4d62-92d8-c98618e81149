&lt;script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import AppLayout from '@/layouts/app/AppSidebarLayout.vue';

const breadcrumbs = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];
&lt;/script>

&lt;template>
    &lt;Head title="Dashboard" />

    &lt;AppLayout :breadcrumbs="breadcrumbs">
        &lt;div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            &lt;div>
                &lt;h1 class="text-3xl font-bold">Dashboard&lt;/h1>
                &lt;p class="text-muted-foreground">Welcome to your dashboard&lt;/p>
            &lt;/div>

            &lt;div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                &lt;div class="rounded-lg border bg-card p-4">
                    &lt;div class="text-sm font-medium text-muted-foreground">Total Purchase Orders&lt;/div>
                    &lt;div class="mt-2 text-2xl font-bold">0&lt;/div>
                &lt;/div>
                &lt;div class="rounded-lg border bg-card p-4">
                    &lt;div class="text-sm font-medium text-muted-foreground">Active Supplies&lt;/div>
                    &lt;div class="mt-2 text-2xl font-bold">0&lt;/div>
                &lt;/div>
                &lt;div class="rounded-lg border bg-card p-4">
                    &lt;div class="text-sm font-medium text-muted-foreground">Pending Documents&lt;/div>
                    &lt;div class="mt-2 text-2xl font-bold">0&lt;/div>
                &lt;/div>
                &lt;div class="rounded-lg border bg-card p-4">
                    &lt;div class="text-sm font-medium text-muted-foreground">Outstanding Payments&lt;/div>
                    &lt;div class="mt-2 text-2xl font-bold">0&lt;/div>
                &lt;/div>
            &lt;/div>

            &lt;div class="grid gap-6 md:grid-cols-2">
                &lt;div class="rounded-lg border bg-card">
                    &lt;div class="p-6">
                        &lt;h2 class="text-lg font-semibold">Recent Purchase Orders&lt;/h2>
                        &lt;div class="mt-4 text-center text-sm text-muted-foreground">
                            No recent purchase orders
                        &lt;/div>
                    &lt;/div>
                &lt;/div>

                &lt;div class="rounded-lg border bg-card">
                    &lt;div class="p-6">
                        &lt;h2 class="text-lg font-semibold">Recent Payments&lt;/h2>
                        &lt;div class="mt-4 text-center text-sm text-muted-foreground">
                            No recent payments
                        &lt;/div>
                    &lt;/div>
                &lt;/div>
            &lt;/div>
        &lt;/div>
    &lt;/AppLayout>
&lt;/template>
