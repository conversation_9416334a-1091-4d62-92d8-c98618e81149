<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';

interface AuthUser {
    name: string;
    role: string;
    // add other user properties as needed
}
interface PageProps {
    auth?: {
        user?: AuthUser;
    };
    // add other props as needed
    [key: string]: unknown;
}

const page = usePage<PageProps>();
const user = page.props.auth?.user;

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];
</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div>
                <h1 class="text-3xl font-bold">Supply System Dashboard</h1>
                <p class="text-muted-foreground">Welcome, {{ user?.name }}! You are logged in as {{ user?.role }}.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- PO Received Card -->
                <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-blue-600 mb-2">PO Received</h3>
                    <p class="text-muted-foreground text-sm mb-4">
                        Manage purchase orders received from institutions
                    </p>
                    <a href="/po-received" class="text-blue-600 hover:text-blue-800 font-medium text-sm">
                        View All →
                    </a>
                </div>

                <!-- Supply Management Card -->
                <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-green-600 mb-2">Supply</h3>
                    <p class="text-muted-foreground text-sm mb-4">
                        Process partial or full supplies for PO orders
                    </p>
                    <a href="/supply" class="text-green-600 hover:text-green-800 font-medium text-sm">
                        View All →
                    </a>
                </div>

                <!-- Documents Card -->
                <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-purple-600 mb-2">Documents</h3>
                    <p class="text-muted-foreground text-sm mb-4">
                        View and manage DC and Invoice documents
                    </p>
                    <a href="/documents" class="text-purple-600 hover:text-purple-800 font-medium text-sm">
                        View All →
                    </a>
                </div>

                <!-- Payments Card -->
                <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-orange-600 mb-2">Payments</h3>
                    <p class="text-muted-foreground text-sm mb-4">
                        Track and manage payment transactions
                    </p>
                    <a href="/payments" class="text-orange-600 hover:text-orange-800 font-medium text-sm">
                        View All →
                    </a>
                </div>
            </div>

            <!-- Admin Section -->
            <div v-if="user?.role === 'admin'" class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                <h3 class="text-lg font-semibold text-red-600 mb-2">Admin Panel</h3>
                <p class="text-muted-foreground mb-4">
                    Administrative functions for managing the system
                </p>
                <a href="/users" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm">
                    Manage Users
                </a>
            </div>

            <!-- System Info -->
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                <h3 class="text-lg font-semibold mb-4">System Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <p class="font-medium">Currency</p>
                        <p class="text-muted-foreground">PKR (Pakistani Rupee)</p>
                    </div>
                    <div>
                        <p class="font-medium">Features</p>
                        <p class="text-muted-foreground">PO Management, Supply Tracking</p>
                    </div>
                    <div>
                        <p class="font-medium">Documents</p>
                        <p class="text-muted-foreground">DC & Invoice Generation</p>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
