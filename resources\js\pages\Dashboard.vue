<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import PlaceholderPattern from '../components/PlaceholderPattern.vue';
const breadcrumbs = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];
</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div>
                <h1 class="text-3xl font-bold">Dashboard</h1>
                <p class="text-muted-foreground">Welcome to your dashboard</p>
            </div>

            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div class="rounded-lg border bg-card p-4">
                    <div class="text-sm font-medium text-muted-foreground">Total Purchase Orders</div>
                    <div class="mt-2 text-2xl font-bold">0</div>
                </div>
                <div class="rounded-lg border bg-card p-4">
                    <div class="text-sm font-medium text-muted-foreground">Active Supplies</div>
                    <div class="mt-2 text-2xl font-bold">0</div>
                </div>
                <div class="rounded-lg border bg-card p-4">
                    <div class="text-sm font-medium text-muted-foreground">Pending Documents</div>
                    <div class="mt-2 text-2xl font-bold">0</div>
                </div>
                <div class="rounded-lg border bg-card p-4">
                    <div class="text-sm font-medium text-muted-foreground">Outstanding Payments</div>
                    <div class="mt-2 text-2xl font-bold">0</div>
                </div>
            </div>

            <div class="grid gap-6 md:grid-cols-2">
                <div class="rounded-lg border bg-card">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold">Recent Purchase Orders</h2>
                        <div class="mt-4 text-center text-sm text-muted-foreground">
                            No recent purchase orders
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border bg-card">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold">Recent Payments</h2>
                        <div class="mt-4 text-center text-sm text-muted-foreground">
                            No recent payments
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
