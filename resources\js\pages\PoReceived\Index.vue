<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Plus, Eye, Edit, Trash2 } from 'lucide-vue-next';

interface PoReceived {
    id: number;
    po_number: string;
    po_date: string;
    institution_name: string;
    email: string;
    phone: string;
    grand_total: number;
    currency: string;
    status: string;
    created_at: string;
    user: {
        name: string;
    };
    items: Array<{
        id: number;
        product_name: string;
        quantity: number;
        price: number;
        total: number;
    }>;
}

defineProps<{
    poReceived: PoReceived[];
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'PO Received',
        href: '/po-received',
    },
];

const deletePo = (poId: number) => {
    if (confirm('Are you sure you want to delete this PO?')) {
        router.delete(`/po-received/${poId}`);
    }
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'partially_supplied':
            return 'bg-blue-100 text-blue-800';
        case 'fully_supplied':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head title="PO Received" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Purchase Orders Received</h1>
                    <p class="text-muted-foreground">Manage purchase orders from institutions</p>
                </div>
                <Link href="/po-received/create">
                    <Button class="flex items-center gap-2">
                        <Plus class="h-4 w-4" />
                        Add PO
                    </Button>
                </Link>
            </div>

            <div class="rounded-lg border bg-card">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-3 px-4 font-semibold">PO Number</th>
                                    <th class="text-left py-3 px-4 font-semibold">Institution</th>
                                    <th class="text-left py-3 px-4 font-semibold">Date</th>
                                    <th class="text-left py-3 px-4 font-semibold">Total</th>
                                    <th class="text-left py-3 px-4 font-semibold">Status</th>
                                    <th class="text-left py-3 px-4 font-semibold">Created By</th>
                                    <th class="text-left py-3 px-4 font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="po in poReceived" :key="po.id" class="border-b hover:bg-muted/50">
                                    <td class="py-3 px-4 font-medium">{{ po.po_number }}</td>
                                    <td class="py-3 px-4">{{ po.institution_name }}</td>
                                    <td class="py-3 px-4">{{ new Date(po.po_date).toLocaleDateString() }}</td>
                                    <td class="py-3 px-4">{{ po.currency }} {{ Number(po.grand_total).toLocaleString() }}</td>
                                    <td class="py-3 px-4">
                                        <span 
                                            :class="getStatusColor(po.status)"
                                            class="px-2 py-1 rounded-full text-xs font-medium"
                                        >
                                            {{ po.status.replace('_', ' ').toUpperCase() }}
                                        </span>
                                    </td>
                                    <td class="py-3 px-4">{{ po.user.name }}</td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-2">
                                            <Link :href="`/po-received/${po.id}`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Eye class="h-3 w-3" />
                                                    View
                                                </Button>
                                            </Link>
                                            <Link :href="`/po-received/${po.id}/edit`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Edit class="h-3 w-3" />
                                                    Edit
                                                </Button>
                                            </Link>
                                            <Button 
                                                variant="outline" 
                                                size="sm" 
                                                class="flex items-center gap-1 text-red-600 hover:text-red-700"
                                                @click="deletePo(po.id)"
                                            >
                                                <Trash2 class="h-3 w-3" />
                                                Delete
                                            </Button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div v-if="poReceived.length === 0" class="text-center py-8 text-muted-foreground">
                            No purchase orders found. <Link href="/po-received/create" class="text-primary hover:underline">Create the first PO</Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
