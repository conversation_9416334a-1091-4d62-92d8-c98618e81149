<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Plus, Eye, Edit } from 'lucide-vue-next';

interface Payment {
    id: number;
    amount: number;
    cheque_number: string;
    cheque_date: string;
    cheque_image: string | null;
    total_amount: number;
    status: string;
    created_at: string;
    supply: {
        id: number;
        supply_type: string;
        supplied_amount: number;
        poReceived: {
            po_number: string;
            institution_name: string;
            currency: string;
        };
    };
    user: {
        name: string;
    };
}

defineProps<{
    payments: Payment[];
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Payments',
        href: '/payments',
    },
];

const updateStatus = (paymentId: number, status: string) => {
    if (confirm(`Are you sure you want to mark this payment as ${status}?`)) {
        router.patch(`/payments/${paymentId}/status`, { status });
    }
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'cleared':
            return 'bg-green-100 text-green-800';
        case 'bounced':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head title="Payments" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Payment Management</h1>
                    <p class="text-muted-foreground">Track and manage payment transactions</p>
                </div>
                <Link href="/payments/create">
                    <Button class="flex items-center gap-2">
                        <Plus class="h-4 w-4" />
                        Add Payment
                    </Button>
                </Link>
            </div>

            <div class="rounded-lg border bg-card">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-3 px-4 font-semibold">PO Number</th>
                                    <th class="text-left py-3 px-4 font-semibold">Institution</th>
                                    <th class="text-left py-3 px-4 font-semibold">Amount</th>
                                    <th class="text-left py-3 px-4 font-semibold">Cheque Number</th>
                                    <th class="text-left py-3 px-4 font-semibold">Cheque Date</th>
                                    <th class="text-left py-3 px-4 font-semibold">Status</th>
                                    <th class="text-left py-3 px-4 font-semibold">Processed By</th>
                                    <th class="text-left py-3 px-4 font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="payment in payments" :key="payment.id" class="border-b hover:bg-muted/50">
                                    <td class="py-3 px-4 font-medium">{{ payment.supply.poReceived.po_number }}</td>
                                    <td class="py-3 px-4">{{ payment.supply.poReceived.institution_name }}</td>
                                    <td class="py-3 px-4">
                                        {{ payment.supply.poReceived.currency }} {{ Number(payment.amount).toLocaleString() }}
                                    </td>
                                    <td class="py-3 px-4">{{ payment.cheque_number }}</td>
                                    <td class="py-3 px-4">{{ new Date(payment.cheque_date).toLocaleDateString() }}</td>
                                    <td class="py-3 px-4">
                                        <span 
                                            :class="getStatusColor(payment.status)"
                                            class="px-2 py-1 rounded-full text-xs font-medium"
                                        >
                                            {{ payment.status.toUpperCase() }}
                                        </span>
                                    </td>
                                    <td class="py-3 px-4">{{ payment.user.name }}</td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-2">
                                            <Link :href="`/payments/${payment.id}`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Eye class="h-3 w-3" />
                                                    View
                                                </Button>
                                            </Link>
                                            
                                            <!-- Status Update Buttons -->
                                            <div v-if="payment.status === 'pending'" class="flex gap-1">
                                                <Button 
                                                    variant="outline" 
                                                    size="sm" 
                                                    class="text-green-600 hover:text-green-700"
                                                    @click="updateStatus(payment.id, 'cleared')"
                                                >
                                                    Clear
                                                </Button>
                                                <Button 
                                                    variant="outline" 
                                                    size="sm" 
                                                    class="text-red-600 hover:text-red-700"
                                                    @click="updateStatus(payment.id, 'bounced')"
                                                >
                                                    Bounce
                                                </Button>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div v-if="payments.length === 0" class="text-center py-8 text-muted-foreground">
                            No payments found. <Link href="/payments/create" class="text-primary hover:underline">Create the first payment</Link>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-yellow-600 mb-2">Pending</h3>
                    <p class="text-3xl font-bold">{{ payments.filter(p => p.status === 'pending').length }}</p>
                    <p class="text-sm text-muted-foreground">Awaiting clearance</p>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-green-600 mb-2">Cleared</h3>
                    <p class="text-3xl font-bold">{{ payments.filter(p => p.status === 'cleared').length }}</p>
                    <p class="text-sm text-muted-foreground">Successfully cleared</p>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-red-600 mb-2">Bounced</h3>
                    <p class="text-3xl font-bold">{{ payments.filter(p => p.status === 'bounced').length }}</p>
                    <p class="text-sm text-muted-foreground">Payment bounced</p>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h3 class="text-lg font-semibold text-blue-600 mb-2">Total Amount</h3>
                    <p class="text-2xl font-bold">
                        PKR {{ payments.reduce((sum, p) => sum + Number(p.amount), 0).toLocaleString() }}
                    </p>
                    <p class="text-sm text-muted-foreground">All payments</p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
