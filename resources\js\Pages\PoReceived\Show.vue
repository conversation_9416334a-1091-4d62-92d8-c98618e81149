<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, Package, Truck, CreditCard } from 'lucide-vue-next';

interface PoReceived {
    id: number;
    po_number: string;
    po_date: string;
    po_image: string | null;
    institution_name: string;
    email: string;
    phone: string;
    address: string;
    grand_total: number;
    currency: string;
    status: string;
    created_at: string;
    user: {
        name: string;
    };
    items: Array<{
        id: number;
        product_name: string;
        price: number;
        quantity: number;
        batch_no: string;
        mfg_date: string;
        expiry_date: string;
        total: number;
        supplied_quantity: number;
    }>;
    supplies: Array<{
        id: number;
        supply_type: string;
        supplied_amount: number;
        created_at: string;
        user: {
            name: string;
        };
    }>;
    total_supplied_amount: number;
    remaining_amount: number;
}

const props = defineProps<{
    poReceived: PoReceived;
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'PO Received',
        href: '/po-received',
    },
    {
        title: props.poReceived.po_number,
        href: `/po-received/${props.poReceived.id}`,
    },
];

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'partially_supplied':
            return 'bg-blue-100 text-blue-800';
        case 'fully_supplied':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getSupplyTypeColor = (type: string) => {
    switch (type) {
        case 'partial':
            return 'bg-blue-100 text-blue-800';
        case 'full':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head :title="poReceived.po_number" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <a href="/po-received" class="flex items-center gap-2 text-muted-foreground hover:text-foreground">
                        <ArrowLeft class="h-4 w-4" />
                        Back to PO List
                    </a>
                </div>
                <div class="flex items-center gap-2">
                    <Link :href="`/po-received/${poReceived.id}/edit`">
                        <Button variant="outline" class="flex items-center gap-2">
                            <Edit class="h-4 w-4" />
                            Edit PO
                        </Button>
                    </Link>
                    <Link href="/supply/create">
                        <Button class="flex items-center gap-2">
                            <Truck class="h-4 w-4" />
                            Process Supply
                        </Button>
                    </Link>
                </div>
            </div>

            <div>
                <h1 class="text-3xl font-bold">{{ poReceived.po_number }}</h1>
                <div class="flex items-center gap-4 mt-2">
                    <span 
                        :class="getStatusColor(poReceived.status)"
                        class="px-3 py-1 rounded-full text-sm font-medium"
                    >
                        {{ poReceived.status.replace('_', ' ').toUpperCase() }}
                    </span>
                    <span class="text-muted-foreground">
                        Created by {{ poReceived.user.name }} on {{ new Date(poReceived.created_at).toLocaleDateString() }}
                    </span>
                </div>
            </div>

            <!-- PO Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">PO Details</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">PO Number:</span>
                            <span>{{ poReceived.po_number }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">PO Date:</span>
                            <span>{{ new Date(poReceived.po_date).toLocaleDateString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Total Amount:</span>
                            <span class="font-semibold">{{ poReceived.currency }} {{ Number(poReceived.grand_total).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Supplied Amount:</span>
                            <span>{{ poReceived.currency }} {{ Number(poReceived.total_supplied_amount).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Remaining Amount:</span>
                            <span>{{ poReceived.currency }} {{ Number(poReceived.remaining_amount).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Created By:</span>
                            <span>{{ poReceived.user.name }}</span>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Institution Information</h2>
                    <div class="space-y-3">
                        <div>
                            <span class="font-medium">Name:</span>
                            <p>{{ poReceived.institution_name }}</p>
                        </div>
                        <div>
                            <span class="font-medium">Email:</span>
                            <p>{{ poReceived.email }}</p>
                        </div>
                        <div>
                            <span class="font-medium">Phone:</span>
                            <p>{{ poReceived.phone }}</p>
                        </div>
                        <div>
                            <span class="font-medium">Address:</span>
                            <p>{{ poReceived.address }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PO Image -->
            <div v-if="poReceived.po_image" class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4">PO Image</h2>
                <div class="border rounded-lg p-4">
                    <img 
                        :src="`/storage/${poReceived.po_image}`" 
                        alt="PO Image"
                        class="max-w-full h-auto max-h-96 mx-auto rounded-lg shadow-sm"
                    />
                </div>
            </div>

            <!-- Items -->
            <div class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Package class="h-5 w-5" />
                    Items ({{ poReceived.items.length }})
                </h2>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-3 px-4 font-semibold">Product</th>
                                <th class="text-left py-3 px-4 font-semibold">Price</th>
                                <th class="text-left py-3 px-4 font-semibold">Quantity</th>
                                <th class="text-left py-3 px-4 font-semibold">Supplied</th>
                                <th class="text-left py-3 px-4 font-semibold">Batch No</th>
                                <th class="text-left py-3 px-4 font-semibold">Mfg Date</th>
                                <th class="text-left py-3 px-4 font-semibold">Expiry Date</th>
                                <th class="text-left py-3 px-4 font-semibold">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in poReceived.items" :key="item.id" class="border-b">
                                <td class="py-3 px-4 font-medium">{{ item.product_name }}</td>
                                <td class="py-3 px-4">{{ poReceived.currency }} {{ Number(item.price).toLocaleString() }}</td>
                                <td class="py-3 px-4">{{ item.quantity }}</td>
                                <td class="py-3 px-4">
                                    <span :class="item.supplied_quantity >= item.quantity ? 'text-green-600' : 'text-yellow-600'">
                                        {{ item.supplied_quantity }} / {{ item.quantity }}
                                    </span>
                                </td>
                                <td class="py-3 px-4">{{ item.batch_no }}</td>
                                <td class="py-3 px-4">{{ new Date(item.mfg_date).toLocaleDateString() }}</td>
                                <td class="py-3 px-4">{{ new Date(item.expiry_date).toLocaleDateString() }}</td>
                                <td class="py-3 px-4 font-medium">{{ poReceived.currency }} {{ Number(item.total).toLocaleString() }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Supply History -->
            <div class="rounded-lg border bg-card p-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Truck class="h-5 w-5" />
                    Supply History ({{ poReceived.supplies.length }})
                </h2>
                <div v-if="poReceived.supplies.length > 0" class="space-y-4">
                    <div v-for="supply in poReceived.supplies" :key="supply.id" class="border rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="flex items-center gap-2">
                                    <span 
                                        :class="getSupplyTypeColor(supply.supply_type)"
                                        class="px-2 py-1 rounded-full text-xs font-medium"
                                    >
                                        {{ supply.supply_type.toUpperCase() }}
                                    </span>
                                    <span class="font-medium">{{ poReceived.currency }} {{ Number(supply.supplied_amount).toLocaleString() }}</span>
                                </div>
                                <p class="text-sm text-muted-foreground mt-1">
                                    Processed by {{ supply.user.name }} on {{ new Date(supply.created_at).toLocaleDateString() }}
                                </p>
                            </div>
                            <div>
                                <Link :href="`/supply/${supply.id}`">
                                    <Button variant="outline" size="sm">View Details</Button>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="text-center py-8 text-muted-foreground">
                    No supplies processed yet. <Link href="/supply/create" class="text-primary hover:underline">Process the first supply</Link>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="rounded-lg border bg-muted/50 p-6">
                <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Link href="/supply/create">
                        <Button class="w-full flex items-center gap-2">
                            <Truck class="h-4 w-4" />
                            Process Supply
                        </Button>
                    </Link>
                    <Link href="/payments/create">
                        <Button variant="outline" class="w-full flex items-center gap-2">
                            <CreditCard class="h-4 w-4" />
                            Process Payment
                        </Button>
                    </Link>
                    <Link :href="`/po-received/${poReceived.id}/edit`">
                        <Button variant="outline" class="w-full flex items-center gap-2">
                            <Edit class="h-4 w-4" />
                            Edit PO
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
