<?php

namespace App\Http\Controllers;

use App\Models\PoReceived;
use App\Models\Supply;
use App\Models\Document;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class SupplyController extends Controller
{
    public function index()
    {
        $supplies = Supply::with(['poReceived', 'user', 'documents'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Supply/Index', [
            'supplies' => $supplies
        ]);
    }

    public function create()
    {
        $poReceived = PoReceived::where('status', '!=', 'fully_supplied')
            ->with('items')
            ->get();

        return Inertia::render('Supply/Create', [
            'poReceived' => $poReceived
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'po_received_id' => 'required|exists:po_received,id',
            'supply_type' => 'required|in:partial,full',
            'supplied_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
            'generate_documents' => 'required|boolean',
        ]);

        $poReceived = PoReceived::findOrFail($request->po_received_id);

        // Check if supplied amount doesn't exceed remaining amount
        $remainingAmount = $poReceived->grand_total - $poReceived->supplies()->sum('supplied_amount');
        if ($request->supplied_amount > $remainingAmount) {
            return back()->withErrors(['supplied_amount' => 'Supplied amount cannot exceed remaining amount.']);
        }

        $supply = Supply::create([
            'po_received_id' => $request->po_received_id,
            'user_id' => auth()->id(),
            'supply_type' => $request->supply_type,
            'supplied_amount' => $request->supplied_amount,
            'notes' => $request->notes,
        ]);

        // Update PO status
        $totalSupplied = $poReceived->supplies()->sum('supplied_amount') + $request->supplied_amount;
        if ($totalSupplied >= $poReceived->grand_total || $request->supply_type === 'full') {
            $poReceived->update(['status' => 'fully_supplied']);
        } else {
            $poReceived->update(['status' => 'partially_supplied']);
        }

        // Generate documents if requested
        if ($request->generate_documents) {
            $this->generateDocuments($supply);
        }

        return redirect()->route('supply.index')->with('success', 'Supply created successfully.');
    }

    public function show(Supply $supply)
    {
        $supply->load(['poReceived.items', 'user', 'documents', 'payments']);

        return Inertia::render('Supply/Show', [
            'supply' => $supply
        ]);
    }

    private function generateDocuments(Supply $supply)
    {
        $supply->load(['poReceived.items', 'user']);

        // Generate DC (Delivery Challan)
        $dcPdf = Pdf::loadView('documents.dc', compact('supply'));
        $dcPath = 'documents/dc_' . $supply->id . '_' . time() . '.pdf';
        Storage::disk('public')->put($dcPath, $dcPdf->output());

        Document::create([
            'supply_id' => $supply->id,
            'document_type' => 'dc',
            'document_path' => $dcPath,
        ]);

        // Generate Invoice
        $invoicePdf = Pdf::loadView('documents.invoice', compact('supply'));
        $invoicePath = 'documents/invoice_' . $supply->id . '_' . time() . '.pdf';
        Storage::disk('public')->put($invoicePath, $invoicePdf->output());

        Document::create([
            'supply_id' => $supply->id,
            'document_type' => 'invoice',
            'document_path' => $invoicePath,
        ]);
    }

    public function generateDocumentsManually(Supply $supply)
    {
        $this->generateDocuments($supply);

        return redirect()->back()->with('success', 'Documents generated successfully.');
    }
}
