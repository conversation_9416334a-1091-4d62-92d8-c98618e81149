<script setup lang="ts">
import { computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthSimpleLayout from '@/layouts/auth/AuthSimpleLayout.vue';
import { Button } from '@/components/ui/button';

defineProps<{
    status?: string;
}>();

const form = useForm({});

const submit = () => {
    form.post(route('verification.send'));
};

const verificationLinkSent = computed(() => form.recentlySuccessful);
</script>

<template>
    <Head title="Email Verification" />

    <AuthSimpleLayout title="Verify your email" description="Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you?">
        <div class="mb-4 text-sm text-gray-600">
            Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.
        </div>

        <div v-if="verificationLinkSent" class="mb-4 text-sm font-medium text-green-600">
            A new verification link has been sent to the email address you provided during registration.
        </div>

        <form @submit.prevent="submit" class="space-y-6">
            <div class="flex items-center justify-between">
                <Button type="submit" :disabled="form.processing">
                    {{ form.processing ? 'Sending...' : 'Resend Verification Email' }}
                </Button>

                <Link
                    :href="route('logout')"
                    method="post"
                    as="button"
                    class="text-sm text-gray-600 underline hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                    Log Out
                </Link>
            </div>
        </form>
    </AuthSimpleLayout>
</template>
