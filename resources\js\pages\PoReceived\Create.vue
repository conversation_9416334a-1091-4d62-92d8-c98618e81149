<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import { ArrowLeft, Plus, Trash2 } from 'lucide-vue-next';
import { ref } from 'vue';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'PO Received',
        href: '/po-received',
    },
    {
        title: 'Create PO',
        href: '/po-received/create',
    },
];

const form = useForm({
    po_number: '',
    po_date: '',
    po_image: null as File | null,
    institution_name: '',
    email: '',
    phone: '',
    address: '',
    items: [
        {
            product_name: '',
            price: 0,
            quantity: 1,
            batch_no: '',
            mfg_date: '',
            expiry_date: '',
            total: 0,
        }
    ],
});

const addItem = () => {
    form.items.push({
        product_name: '',
        price: 0,
        quantity: 1,
        batch_no: '',
        mfg_date: '',
        expiry_date: '',
        total: 0,
    });
};

const removeItem = (index: number) => {
    if (form.items.length > 1) {
        form.items.splice(index, 1);
    }
};

const calculateItemTotal = (index: number) => {
    const item = form.items[index];
    item.total = item.price * item.quantity;
};

const submit = () => {
    form.post('/po-received', {
        forceFormData: true,
        onSuccess: () => {
            // Redirect will be handled by the controller
        },
    });
};

const handleFileChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        form.po_image = target.files[0];
    }
};
</script>

<template>
    <Head title="Create PO Received" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full w-fullflex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center gap-4">
                <a href="/po-received" class="flex items-center gap-2 text-muted-foreground hover:text-foreground">
                    <ArrowLeft class="h-4 w-4" />
                    Back to PO Received
                </a>
            </div>

            <div>
                <h1 class="text-3xl font-bold">Create Purchase Order</h1>
                <p class="text-muted-foreground">Add a new purchase order received from institution</p>
            </div>

            <div class="max-w-4xl">
                <form @submit.prevent="submit" class="space-y-8">
                    <!-- PO Information -->
                    <div class="rounded-lg border bg-card p-6">
                        <h2 class="text-xl font-semibold mb-4">PO Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="grid gap-2">
                                <Label for="po_number">PO Number</Label>
                                <Input
                                    id="po_number"
                                    v-model="form.po_number"
                                    type="text"
                                    placeholder="Enter PO number"
                                    required
                                />
                                <InputError :message="form.errors.po_number" />
                            </div>

                            <div class="grid gap-2">
                                <Label for="po_date">PO Date</Label>
                                <Input
                                    id="po_date"
                                    v-model="form.po_date"
                                    type="date"
                                    required
                                />
                                <InputError :message="form.errors.po_date" />
                            </div>

                            <div class="grid gap-2 md:col-span-2">
                                <Label for="po_image">PO Image (Optional)</Label>
                                <Input
                                    id="po_image"
                                    type="file"
                                    accept="image/*"
                                    @change="handleFileChange"
                                />
                                <InputError :message="form.errors.po_image" />
                            </div>
                        </div>
                    </div>

                    <!-- Institution Information -->
                    <div class="rounded-lg border bg-card p-6">
                        <h2 class="text-xl font-semibold mb-4">Institution Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="grid gap-2">
                                <Label for="institution_name">Institution/Hospital Name</Label>
                                <Input
                                    id="institution_name"
                                    v-model="form.institution_name"
                                    type="text"
                                    placeholder="Enter institution name"
                                    required
                                />
                                <InputError :message="form.errors.institution_name" />
                            </div>

                            <div class="grid gap-2">
                                <Label for="email">Email</Label>
                                <Input
                                    id="email"
                                    v-model="form.email"
                                    type="email"
                                    placeholder="Enter email address"
                                    required
                                />
                                <InputError :message="form.errors.email" />
                            </div>

                            <div class="grid gap-2">
                                <Label for="phone">Phone</Label>
                                <Input
                                    id="phone"
                                    v-model="form.phone"
                                    type="tel"
                                    placeholder="Enter phone number"
                                    required
                                />
                                <InputError :message="form.errors.phone" />
                            </div>

                            <div class="grid gap-2">
                                <Label for="address">Address</Label>
                                <textarea
                                    id="address"
                                    v-model="form.address"
                                    class="flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                                    placeholder="Enter complete address"
                                    required
                                ></textarea>
                                <InputError :message="form.errors.address" />
                            </div>
                        </div>
                    </div>

                    <!-- Items -->
                    <div class="rounded-lg border bg-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-xl font-semibold">Items</h2>
                            <Button type="button" @click="addItem" class="flex items-center gap-2">
                                <Plus class="h-4 w-4" />
                                Add Item
                            </Button>
                        </div>

                        <div v-for="(item, index) in form.items" :key="index" class="border rounded-lg p-4 mb-4">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="font-medium">Item {{ index + 1 }}</h3>
                                <Button 
                                    v-if="form.items.length > 1"
                                    type="button" 
                                    variant="outline" 
                                    size="sm"
                                    @click="removeItem(index)"
                                    class="text-red-600 hover:text-red-700"
                                >
                                    <Trash2 class="h-4 w-4" />
                                </Button>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                                <div class="grid gap-2">
                                    <Label :for="`product_name_${index}`">Product Name</Label>
                                    <Input
                                        :id="`product_name_${index}`"
                                        v-model="item.product_name"
                                        type="text"
                                        placeholder="Enter product name"
                                        required
                                    />
                                </div>

                                <div class="grid gap-2">
                                    <Label :for="`price_${index}`">Price (PKR)</Label>
                                    <Input
                                        :id="`price_${index}`"
                                        v-model.number="item.price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        placeholder="0.00"
                                        @input="calculateItemTotal(index)"
                                        required
                                    />
                                </div>

                                <div class="grid gap-2">
                                    <Label :for="`quantity_${index}`">Quantity</Label>
                                    <Input
                                        :id="`quantity_${index}`"
                                        v-model.number="item.quantity"
                                        type="number"
                                        min="1"
                                        placeholder="1"
                                        @input="calculateItemTotal(index)"
                                        required
                                    />
                                </div>

                                <div class="grid gap-2">
                                    <Label :for="`batch_no_${index}`">Batch No</Label>
                                    <Input
                                        :id="`batch_no_${index}`"
                                        v-model="item.batch_no"
                                        type="text"
                                        placeholder="Enter batch number"
                                        required
                                    />
                                </div>

                                <div class="grid gap-2">
                                    <Label :for="`mfg_date_${index}`">Manufacturing Date</Label>
                                    <Input
                                        :id="`mfg_date_${index}`"
                                        v-model="item.mfg_date"
                                        type="date"
                                        required
                                    />
                                </div>

                                <div class="grid gap-2">
                                    <Label :for="`expiry_date_${index}`">Expiry Date</Label>
                                    <Input
                                        :id="`expiry_date_${index}`"
                                        v-model="item.expiry_date"
                                        type="date"
                                        required
                                    />
                                </div>

                                <div class="grid gap-2 md:col-span-3">
                                    <Label>Total: PKR {{ item.total.toLocaleString() }}</Label>
                                </div>
                            </div>
                        </div>

                        <div class="text-right mt-4">
                            <p class="text-lg font-semibold">
                                Grand Total: PKR {{ form.items.reduce((sum, item) => sum + item.total, 0).toLocaleString() }}
                            </p>
                        </div>
                    </div>

                    <div class="flex items-center gap-4">
                        <Button type="submit" :disabled="form.processing">
                            {{ form.processing ? 'Creating...' : 'Create PO' }}
                        </Button>
                        <a href="/po-received">
                            <Button type="button" variant="outline">
                                Cancel
                            </Button>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>
