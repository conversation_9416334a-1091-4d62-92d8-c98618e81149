<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Supply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class PaymentController extends Controller
{
    public function index()
    {
        $payments = Payment::with(['supply.poReceived', 'user'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Payments/Index', [
            'payments' => $payments
        ]);
    }

    public function create()
    {
        $supplies = Supply::with(['poReceived'])
            ->whereDoesntHave('payments')
            ->orWhereHas('payments', function($query) {
                $query->havingRaw('SUM(amount) < supplies.supplied_amount');
            })
            ->get();

        return Inertia::render('Payments/Create', [
            'supplies' => $supplies
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'supply_id' => 'required|exists:supplies,id',
            'amount' => 'required|numeric|min:0',
            'cheque_number' => 'required|string|max:255',
            'cheque_date' => 'required|date',
            'cheque_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'total_amount' => 'required|numeric|min:0',
        ]);

        $supply = Supply::findOrFail($request->supply_id);

        // Check if payment amount doesn't exceed supply amount
        $existingPayments = $supply->payments->sum('amount');
        $remainingAmount = $supply->supplied_amount - $existingPayments;

        if ($request->amount > $remainingAmount) {
            return back()->withErrors(['amount' => 'Payment amount cannot exceed remaining supply amount.']);
        }

        $chequeImagePath = null;
        if ($request->hasFile('cheque_image')) {
            $chequeImagePath = $request->file('cheque_image')->store('cheque_images', 'public');
        }

        Payment::create([
            'supply_id' => $request->supply_id,
            'user_id' => auth()->id(),
            'amount' => $request->amount,
            'cheque_number' => $request->cheque_number,
            'cheque_date' => $request->cheque_date,
            'cheque_image' => $chequeImagePath,
            'total_amount' => $request->total_amount,
            'status' => 'pending',
        ]);

        return redirect()->route('payments.index')->with('success', 'Payment created successfully.');
    }

    public function show(Payment $payment)
    {
        $payment->load(['supply.poReceived', 'user']);

        return Inertia::render('Payments/Show', [
            'payment' => $payment
        ]);
    }

    public function updateStatus(Request $request, Payment $payment)
    {
        $request->validate([
            'status' => 'required|in:pending,cleared,bounced',
        ]);

        $payment->update([
            'status' => $request->status
        ]);

        return redirect()->back()->with('success', 'Payment status updated successfully.');
    }
}
