<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText, Download, CreditCard } from 'lucide-vue-next';

interface Supply {
    id: number;
    supply_type: string;
    supplied_amount: number;
    notes: string | null;
    created_at: string;
    poReceived: {
        id: number;
        po_number: string;
        po_date: string;
        institution_name: string;
        email: string;
        phone: string;
        address: string;
        grand_total: number;
        currency: string;
        status: string;
        items: Array<{
            id: number;
            product_name: string;
            price: number;
            quantity: number;
            batch_no: string;
            mfg_date: string;
            expiry_date: string;
            total: number;
            supplied_quantity: number;
        }>;
    };
    user: {
        name: string;
        email: string;
    };
    documents: Array<{
        id: number;
        document_type: string;
        document_path: string;
        uploaded_image_path: string | null;
    }>;
    payments: Array<{
        id: number;
        amount: number;
        cheque_number: string;
        cheque_date: string;
        status: string;
        created_at: string;
        user: {
            name: string;
        };
    }>;
}

const props = defineProps<{
    supply: Supply;
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Supply',
        href: '/supply',
    },
    {
        title: `Supply #${props.supply.id}`,
        href: `/supply/${props.supply.id}`,
    },
];

const generateDocuments = () => {
    router.post(`/supply/${props.supply.id}/generate-documents`);
};

const getSupplyTypeColor = (type: string) => {
    switch (type) {
        case 'partial':
            return 'bg-blue-100 text-blue-800';
        case 'full':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getPaymentStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'cleared':
            return 'bg-green-100 text-green-800';
        case 'bounced':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head :title="`Supply #${supply.id}`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <a href="/supply" class="flex items-center gap-2 text-muted-foreground hover:text-foreground">
                        <ArrowLeft class="h-4 w-4" />
                        Back to Supply
                    </a>
                </div>
                <div class="flex items-center gap-2">
                    <Link href="/payments/create">
                        <Button class="flex items-center gap-2">
                            <CreditCard class="h-4 w-4" />
                            Process Payment
                        </Button>
                    </Link>
                </div>
            </div>

            <div>
                <h1 class="text-3xl font-bold">Supply #{{ supply.id }}</h1>
                <div class="flex items-center gap-4 mt-2">
                    <span 
                        :class="getSupplyTypeColor(supply.supply_type)"
                        class="px-3 py-1 rounded-full text-sm font-medium"
                    >
                        {{ supply.supply_type.toUpperCase() }} SUPPLY
                    </span>
                    <span class="text-muted-foreground">
                        Processed by {{ supply.user.name }} on {{ new Date(supply.created_at).toLocaleDateString() }}
                    </span>
                </div>
            </div>

            <!-- Supply Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">Supply Details</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">Supply Type:</span>
                            <span>{{ supply.supply_type.toUpperCase() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Supplied Amount:</span>
                            <span class="font-semibold">{{ supply.poReceived.currency }} {{ Number(supply.supplied_amount).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Total PO Amount:</span>
                            <span>{{ supply.poReceived.currency }} {{ Number(supply.poReceived.grand_total).toLocaleString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Processed By:</span>
                            <span>{{ supply.user.name }}</span>
                        </div>
                        <div v-if="supply.notes" class="pt-3 border-t">
                            <span class="font-medium">Notes:</span>
                            <p class="mt-1 text-sm">{{ supply.notes }}</p>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg border bg-card p-6">
                    <h2 class="text-xl font-semibold mb-4">PO Information</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="font-medium">PO Number:</span>
                            <Link :href="`/po-received/${supply.poReceived.id}`" class="text-blue-600 hover:underline">
                                {{ supply.poReceived.po_number }}
                            </Link>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Institution:</span>
                            <span>{{ supply.poReceived.institution_name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">PO Date:</span>
                            <span>{{ new Date(supply.poReceived.po_date).toLocaleDateString() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="font-medium">Status:</span>
                            <span>{{ supply.poReceived.status.replace('_', ' ').toUpperCase() }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents -->
            <div class="rounded-lg border bg-card p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold">Documents</h2>
                    <Button 
                        v-if="supply.documents.length === 0"
                        @click="generateDocuments"
                        class="flex items-center gap-2"
                    >
                        <FileText class="h-4 w-4" />
                        Generate Documents
                    </Button>
                </div>

                <div v-if="supply.documents.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div v-for="document in supply.documents" :key="document.id" class="border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium">{{ document.document_type.toUpperCase() }}</h3>
                            <div class="flex items-center gap-2">
                                <Link :href="`/documents/${document.id}`">
                                    <Button variant="outline" size="sm">View</Button>
                                </Link>
                                <a :href="`/documents/${document.id}/download`" target="_blank">
                                    <Button variant="outline" size="sm" class="flex items-center gap-1">
                                        <Download class="h-3 w-3" />
                                        Download
                                    </Button>
                                </a>
                            </div>
                        </div>
                        <div v-if="document.uploaded_image_path" class="text-sm text-green-600">
                            ✓ Image uploaded
                        </div>
                        <div v-else class="text-sm text-muted-foreground">
                            No image uploaded
                        </div>
                    </div>
                </div>

                <div v-else class="text-center py-8 text-muted-foreground">
                    No documents generated yet. Click "Generate Documents" to create DC and Invoice.
                </div>
            </div>

            <!-- Payments -->
            <div class="rounded-lg border bg-card p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold">Payments</h2>
                    <Link href="/payments/create">
                        <Button class="flex items-center gap-2">
                            <CreditCard class="h-4 w-4" />
                            Add Payment
                        </Button>
                    </Link>
                </div>

                <div v-if="supply.payments.length > 0" class="space-y-4">
                    <div v-for="payment in supply.payments" :key="payment.id" class="border rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium">{{ supply.poReceived.currency }} {{ Number(payment.amount).toLocaleString() }}</p>
                                <p class="text-sm text-muted-foreground">
                                    Cheque: {{ payment.cheque_number }} | Date: {{ new Date(payment.cheque_date).toLocaleDateString() }}
                                </p>
                                <p class="text-sm text-muted-foreground">
                                    By {{ payment.user.name }} on {{ new Date(payment.created_at).toLocaleDateString() }}
                                </p>
                            </div>
                            <div class="text-right">
                                <span 
                                    :class="getPaymentStatusColor(payment.status)"
                                    class="px-2 py-1 rounded-full text-xs font-medium"
                                >
                                    {{ payment.status.toUpperCase() }}
                                </span>
                                <div class="mt-2">
                                    <Link :href="`/payments/${payment.id}`">
                                        <Button variant="outline" size="sm">View Details</Button>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-else class="text-center py-8 text-muted-foreground">
                    No payments recorded yet. <Link href="/payments/create" class="text-primary hover:underline">Add the first payment</Link>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
