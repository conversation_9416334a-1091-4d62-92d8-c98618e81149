<?php

use App\Http\Controllers\UserController;
use App\Http\Controllers\PoReceivedController;
use App\Http\Controllers\SupplyController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\PaymentController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('Dashboard');
    })->name('dashboard');

    // Admin only routes
    Route::middleware('admin')->group(function () {
        Route::resource('users', UserController::class);
    });

    // User routes
    Route::resource('po-received', PoReceivedController::class);
    Route::resource('supply', SupplyController::class);
    Route::post('supply/{supply}/generate-documents', [SupplyController::class, 'generateDocumentsManually'])
        ->name('supply.generate-documents');

    Route::resource('documents', DocumentController::class)->only(['index', 'show']);
    Route::get('documents/{document}/download', [DocumentController::class, 'download'])
        ->name('documents.download');
    Route::post('documents/{document}/upload-image', [DocumentController::class, 'uploadImage'])
        ->name('documents.upload-image');

    Route::resource('payments', PaymentController::class);
    Route::patch('payments/{payment}/status', [PaymentController::class, 'updateStatus'])
        ->name('payments.update-status');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
