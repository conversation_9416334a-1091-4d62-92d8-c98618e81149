<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('po_received', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('po_number')->unique();
            $table->date('po_date');
            $table->string('po_image')->nullable();
            $table->string('institution_name');
            $table->string('email');
            $table->string('phone');
            $table->text('address');
            $table->decimal('grand_total', 15, 2);
            $table->string('currency', 3)->default('PKR');
            $table->enum('status', ['pending', 'partially_supplied', 'fully_supplied'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('po_received');
    }
};
