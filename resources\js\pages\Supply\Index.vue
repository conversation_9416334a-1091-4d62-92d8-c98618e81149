<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Plus, Eye, FileText } from 'lucide-vue-next';

interface Supply {
    id: number;
    supply_type: string;
    supplied_amount: number;
    notes: string | null;
    created_at: string;
    poReceived: {
        id: number;
        po_number: string;
        institution_name: string;
        grand_total: number;
        currency: string;
        status: string;
    };
    user: {
        name: string;
    };
    documents: Array<{
        id: number;
        document_type: string;
        document_path: string;
    }>;
}

defineProps<{
    supplies: Supply[];
}>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Supply',
        href: '/supply',
    },
];

const getSupplyTypeColor = (type: string) => {
    switch (type) {
        case 'partial':
            return 'bg-blue-100 text-blue-800';
        case 'full':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
</script>

<template>
    <Head title="Supply Management" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Supply Management</h1>
                    <p class="text-muted-foreground">Process and track supply operations</p>
                </div>
                <Link href="/supply/create">
                    <Button class="flex items-center gap-2">
                        <Plus class="h-4 w-4" />
                        Process Supply
                    </Button>
                </Link>
            </div>

            <div class="rounded-lg border bg-card">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left py-3 px-4 font-semibold">PO Number</th>
                                    <th class="text-left py-3 px-4 font-semibold">Institution</th>
                                    <th class="text-left py-3 px-4 font-semibold">Supply Type</th>
                                    <th class="text-left py-3 px-4 font-semibold">Amount</th>
                                    <th class="text-left py-3 px-4 font-semibold">Processed By</th>
                                    <th class="text-left py-3 px-4 font-semibold">Date</th>
                                    <th class="text-left py-3 px-4 font-semibold">Documents</th>
                                    <th class="text-left py-3 px-4 font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="supply in supplies" :key="supply.id" class="border-b hover:bg-muted/50">
                                    <td class="py-3 px-4 font-medium">{{ supply.poReceived.po_number }}</td>
                                    <td class="py-3 px-4">{{ supply.poReceived.institution_name }}</td>
                                    <td class="py-3 px-4">
                                        <span 
                                            :class="getSupplyTypeColor(supply.supply_type)"
                                            class="px-2 py-1 rounded-full text-xs font-medium"
                                        >
                                            {{ supply.supply_type.toUpperCase() }}
                                        </span>
                                    </td>
                                    <td class="py-3 px-4">{{ supply.poReceived.currency }} {{ Number(supply.supplied_amount).toLocaleString() }}</td>
                                    <td class="py-3 px-4">{{ supply.user.name }}</td>
                                    <td class="py-3 px-4">{{ new Date(supply.created_at).toLocaleDateString() }}</td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-1">
                                            <span v-if="supply.documents.length > 0" class="text-green-600 text-sm">
                                                {{ supply.documents.length }} docs
                                            </span>
                                            <span v-else class="text-gray-500 text-sm">
                                                No docs
                                            </span>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center gap-2">
                                            <Link :href="`/supply/${supply.id}`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <Eye class="h-3 w-3" />
                                                    View
                                                </Button>
                                            </Link>
                                            <Link v-if="supply.documents.length > 0" :href="`/documents?supply_id=${supply.id}`">
                                                <Button variant="outline" size="sm" class="flex items-center gap-1">
                                                    <FileText class="h-3 w-3" />
                                                    Documents
                                                </Button>
                                            </Link>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <div v-if="supplies.length === 0" class="text-center py-8 text-muted-foreground">
                            No supplies found. <Link href="/supply/create" class="text-primary hover:underline">Process the first supply</Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
